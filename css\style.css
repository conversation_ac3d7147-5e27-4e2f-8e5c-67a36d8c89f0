/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* 容器布局 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

header p {
    color: #7f8c8d;
    font-size: 1.1em;
}

/* 主要内容区域 */
main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

/* 输入区域 */
.input-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.input-section label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2c3e50;
}

.input-section textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.input-section textarea:focus {
    outline: none;
    border-color: #3498db;
}

.button-group {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.input-section button {
    flex: 1;
    padding: 12px 24px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.input-section button:hover {
    background: #2980b9;
}

.input-section button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.example-btn {
    background: #27ae60 !important;
}

.example-btn:hover {
    background: #229954 !important;
}

/* 输出区域 */
.output-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.output-section h2 {
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

/* 状态样式 */
.loading {
    text-align: center;
    padding: 20px;
    color: #3498db;
    font-style: italic;
}

.error {
    padding: 15px;
    background: #ffe6e6;
    border: 1px solid #e74c3c;
    border-radius: 8px;
    color: #c0392b;
}

.hidden {
    display: none;
}

/* 结果显示区域 */
.result {
    min-height: 200px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #ecf0f1;
}

/* 矢量红色下划线样式 */
.underlined {
    position: relative;
    display: inline;
}

.underlined::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 100%;
    height: 10px;
    background-image: url('./underline.svg');
    background-repeat: repeat-x;
    background-size: 100px 10px;
    pointer-events: none;
}

/* Markdown 样式 */
.result h1, .result h2, .result h3, .result h4, .result h5, .result h6 {
    margin: 20px 0 10px 0;
    color: #2c3e50;
}

.result h1 { font-size: 2em; }
.result h2 { font-size: 1.5em; }
.result h3 { font-size: 1.3em; }

.result p {
    margin: 10px 0;
    line-height: 1.8;
}

.result ul, .result ol {
    margin: 10px 0;
    padding-left: 30px;
}

.result li {
    margin: 5px 0;
}

/* 代码块样式 */
.result pre {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 15px 0;
    font-family: 'Courier New', monospace;
}

.result code {
    background: #ecf0f1;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.result pre code {
    background: none;
    padding: 0;
    color: inherit;
}

/* 特定段落标注样式 */
.special-paragraph {
    position: relative;
    padding: 15px;
    margin: 15px 0;
    background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
    border-left: 4px solid #e74c3c;
    border-radius: 0 8px 8px 0;
}

.special-paragraph::before {
    content: '📌';
    position: absolute;
    left: -12px;
    top: 15px;
    background: #e74c3c;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .container {
        padding: 10px;
    }
    
    .input-section, .output-section {
        padding: 20px;
    }
}
