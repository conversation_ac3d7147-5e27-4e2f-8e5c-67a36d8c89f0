!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define(["react"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).reactHook=t(e.React)}(this,(function(e){"use strict";const t="undefined",n=void 0;typeof window===t&&typeof process!==t&&process.browser;const o=()=>{},r=e=>2 in e?e[2]:e[0],s=e=>e.current,f=(e,t)=>{e.current=t};return{name:"React",create:t=>e.useState(t),export:r,dehydrate:r,update:(e,t)=>{t[2]=e,t[1](e)},memorize:t=>{const n=e.useRef(o);return f(n,t),e.useCallback(((...e)=>s(n)(...e)),[])},ref:t=>{const o=e.useRef(t);return s(o)===n&&f(o,t),o},effectRequest({handler:t,removeStates:o,immediate:r,watchingStates:u=[]}){const c=e.useRef(u);e.useEffect((()=>{const e=s(c);let o=n;for(const t in u)if(!Object.is(e[t],u[t])){o=Number(t);break}var a;f(c,u),!r&&("number"!==(e=>typeof e)(a=o)||Number.isNaN(a))||t(o)}),u),e.useEffect((()=>o),[])},computed:(t,n)=>[e.useMemo(t,n),o],watch:(t,n)=>{const o=e.useRef(false);e.useEffect((()=>{o.current?n():o.current=!0}),t)},onMounted:t=>{e.useEffect(t,[])},onUnmounted:t=>{e.useEffect((()=>t),[])}}}));
