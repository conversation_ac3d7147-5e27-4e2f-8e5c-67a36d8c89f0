// LM Studio API客户端模块
export class LMStudioClient {
    constructor(configManager) {
        this.configManager = configManager;
    }

    // 调用LM Studio进行分析
    async analyze(processedLogString) {
        const prompt = this.configManager.generateAnalysisPrompt(processedLogString);
        const apiUrl = this.configManager.getAPIUrl();
        const requestConfig = this.configManager.getLMStudioRequestConfig(prompt);

        console.log('🤖 正在调用 LM Studio...');
        console.log('API URL:', apiUrl);
        console.log('模型:', requestConfig.model);

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestConfig)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            let content = data.choices[0].message.content;

            // 处理不同模型的特殊标签
            // 移除推理过程标签
            if (content.includes('<think>')) {
                const thinkEndIndex = content.lastIndexOf('</think>');
                if (thinkEndIndex !== -1) {
                    content = content.substring(thinkEndIndex + 8).trim();
                }
            }

            // 清理其他可能的标记
            content = content.replace(/<\/?[^>]+(>|$)/g, "").trim();

            console.log('✅ LM Studio 分析完成');
            console.log('响应内容预览:', content.substring(0, 200) + '...');

            return content;

        } catch (error) {
            console.error('❌ LM Studio 调用失败:', error);
            throw new Error(`LM Studio 请求失败: ${error.message}`);
        }
    }

    // 测试连接
    async testConnection() {
        try {
            const testPrompt = "测试连接，请简单回复'连接成功'";
            const apiUrl = this.configManager.getAPIUrl();
            const requestConfig = this.configManager.getLMStudioRequestConfig(testPrompt);
            
            // 设置较短的超时时间用于测试
            requestConfig.max_tokens = 50;

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestConfig)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('✅ LM Studio 连接测试成功');
            return true;

        } catch (error) {
            console.error('❌ LM Studio 连接测试失败:', error);
            return false;
        }
    }
}
