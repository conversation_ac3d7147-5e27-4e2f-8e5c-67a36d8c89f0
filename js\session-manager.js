// 会话管理模块
export class SessionManager {
    constructor() {
        this.sessionFile = './js/huihua.json';
        this.maxHistoryLength = 10; // 最大保存的对话数量
        this.sessions = [];
        this.currentSessionId = null;
    }

    // 初始化会话管理器
    async init() {
        try {
            await this.loadSessions();
            console.log('✅ 会话管理器初始化成功');
        } catch (error) {
            console.log('📝 创建新的会话历史文件');
            this.sessions = [];
            await this.saveSessions();
        }
    }

    // 从本地文件加载会话历史
    async loadSessions() {
        try {
            const response = await fetch(this.sessionFile);
            if (response.ok) {
                const data = await response.json();
                this.sessions = data.sessions || [];
                console.log(`📚 加载了 ${this.sessions.length} 个历史会话`);
            } else {
                throw new Error('会话文件不存在');
            }
        } catch (error) {
            console.warn('加载会话历史失败:', error.message);
            this.sessions = [];
        }
    }

    // 保存会话历史到本地文件
    async saveSessions() {
        const sessionData = {
            lastUpdated: new Date().toISOString(),
            totalSessions: this.sessions.length,
            sessions: this.sessions
        };

        try {
            // 注意：浏览器环境无法直接写文件，这里提供下载功能
            const blob = new Blob([JSON.stringify(sessionData, null, 2)], {
                type: 'application/json'
            });
            
            console.log('💾 会话数据已准备，可通过以下方式保存:');
            console.log('1. 调用 downloadSessions() 方法下载文件');
            console.log('2. 或复制以下数据到 js/huihua.json:');
            console.log(JSON.stringify(sessionData, null, 2));

            // 自动触发下载（可选）
            this.triggerDownload(blob, 'huihua.json');
            
        } catch (error) {
            console.error('保存会话历史失败:', error);
        }
    }

    // 触发文件下载
    triggerDownload(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // 创建新会话
    createNewSession(inputData) {
        const sessionId = this.generateSessionId();
        const session = {
            id: sessionId,
            timestamp: new Date().toISOString(),
            inputData: inputData,
            messages: [],
            status: 'active'
        };
        
        this.sessions.unshift(session); // 添加到开头
        this.currentSessionId = sessionId;
        
        // 限制历史记录数量
        if (this.sessions.length > this.maxHistoryLength) {
            this.sessions = this.sessions.slice(0, this.maxHistoryLength);
        }
        
        console.log(`🆕 创建新会话: ${sessionId}`);
        return sessionId;
    }

    // 添加消息到当前会话
    addMessage(role, content, metadata = {}) {
        if (!this.currentSessionId) {
            console.warn('没有活动会话，无法添加消息');
            return;
        }

        const session = this.getCurrentSession();
        if (session) {
            const message = {
                role: role,
                content: content,
                timestamp: new Date().toISOString(),
                metadata: metadata
            };
            
            session.messages.push(message);
            console.log(`💬 添加消息到会话 ${this.currentSessionId}: ${role}`);
        }
    }

    // 获取当前会话
    getCurrentSession() {
        return this.sessions.find(session => session.id === this.currentSessionId);
    }

    // 获取当前会话的消息历史
    getCurrentMessages() {
        const session = this.getCurrentSession();
        return session ? session.messages : [];
    }

    // 完成当前会话
    completeCurrentSession() {
        const session = this.getCurrentSession();
        if (session) {
            session.status = 'completed';
            session.completedAt = new Date().toISOString();
            console.log(`✅ 会话 ${this.currentSessionId} 已完成`);
            
            // 保存到本地
            this.saveSessions();
        }
        this.currentSessionId = null;
    }

    // 生成会话ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 获取会话统计信息
    getSessionStats() {
        return {
            totalSessions: this.sessions.length,
            activeSessions: this.sessions.filter(s => s.status === 'active').length,
            completedSessions: this.sessions.filter(s => s.status === 'completed').length,
            currentSessionId: this.currentSessionId
        };
    }

    // 清理旧会话
    cleanupOldSessions(daysOld = 7) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysOld);
        
        const originalLength = this.sessions.length;
        this.sessions = this.sessions.filter(session => {
            const sessionDate = new Date(session.timestamp);
            return sessionDate > cutoffDate;
        });
        
        const removedCount = originalLength - this.sessions.length;
        if (removedCount > 0) {
            console.log(`🧹 清理了 ${removedCount} 个旧会话`);
            this.saveSessions();
        }
    }

    // 导出会话数据
    exportSessions() {
        return {
            exportTime: new Date().toISOString(),
            totalSessions: this.sessions.length,
            sessions: this.sessions
        };
    }

    // 手动下载会话文件
    downloadSessions() {
        const sessionData = this.exportSessions();
        const blob = new Blob([JSON.stringify(sessionData, null, 2)], {
            type: 'application/json'
        });
        this.triggerDownload(blob, 'huihua.json');
    }
}
