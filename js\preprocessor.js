// 安全日志预处理器模块
export class SecurityLogPreprocessor {
    constructor(criticalFields) {
        // 从配置中获取关键字段定义
        this.criticalFields = criticalFields;
    }

    // 预处理安全日志
    preprocessSecurityLog(rawLog) {
        const processed = {
            attackInfo: {},
            networkInfo: {},
            attackDetails: {},
            timeAndLocation: {},
            messageDetails: {}
        };

        // 提取基本字段
        Object.keys(this.criticalFields).forEach(category => {
            this.criticalFields[category].forEach(field => {
                if (rawLog.hasOwnProperty(field)) {
                    processed[category][field] = rawLog[field];
                }
            });
        });

        // 特殊处理 message 字段中的攻击载荷信息
        if (rawLog.message) {
            try {
                const messageObj = JSON.parse(rawLog.message);
                processed.messageDetails = {
                    requestUrl: messageObj.requestUrl || messageObj.requestUrlQuery || messageObj.uri || '',
                    requestBody: messageObj.requestBody || '',
                    requestHeader: messageObj.requestHeader || '',
                    requestMethod: messageObj.requestMethod || messageObj.method || '',
                    tacticId: messageObj.tacticId || '',
                    techniquesId: messageObj.techniquesId || ''
                };
            } catch (e) {
                console.warn('无法解析 message 字段:', e.message);
                processed.messageDetails = { error: '消息解析失败' };
            }
        }

        return processed;
    }

    // 在终端打印预处理结果
    printProcessedLog(processedLog) {
        console.log('\n=== 安全日志预处理结果 ===\n');
        
        console.log('🚨 攻击基本信息:');
        Object.entries(processedLog.attackInfo).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        console.log('\n🌐 网络连接信息:');
        Object.entries(processedLog.networkInfo).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        console.log('\n⚔️ 攻击详情:');
        Object.entries(processedLog.attackDetails).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        console.log('\n📍 时间和地理位置:');
        Object.entries(processedLog.timeAndLocation).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        console.log('\n📝 攻击载荷详情:');
        Object.entries(processedLog.messageDetails).forEach(([key, value]) => {
            if (value && value.toString().length > 100) {
                console.log(`  ${key}: ${value.toString().substring(0, 100)}...`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        });

        console.log('\n=== 预处理完成 ===\n');
        
        return processedLog;
    }
}
