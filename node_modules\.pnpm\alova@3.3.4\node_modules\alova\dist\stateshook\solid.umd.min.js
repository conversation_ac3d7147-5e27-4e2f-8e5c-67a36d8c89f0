!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o(require("solid-js")):"function"==typeof define&&define.amd?define(["solid-js"],o):(e="undefined"!=typeof globalThis?globalThis:e||self).solidHook=o(e.Solid)}(this,(function(e){"use strict";const o="undefined";typeof window===o&&typeof process!==o&&process.browser;const t=()=>{},n=(e=0)=>{let o;return t=>{o&&clearTimeout(o),o=((e,o=0)=>setTimeout(e,o))(t,e)}};return{name:"Solid",create:o=>e.createSignal(o),export:e=>e[0],dehydrate:e=>e[0](),update:(e,o)=>{o[1](e)},effectRequest:({handler:o,removeStates:t,immediate:n,watchingStates:r=[]})=>{var d;e.onCleanup(t),n&&o(),d=(t,n)=>{e.createEffect(e.on(t,(()=>{o(n)}),{defer:!0}))},r.forEach(d)},computed:o=>[e.createMemo(o),t],watch:(o,t)=>{const r=Array.isArray(o)?o:[o],d=n();e.createEffect(e.on(r.map((e=>e)),(()=>d((()=>{t()}))),{defer:!0}))},onMounted:o=>{e.onMount(o)},onUnmounted:o=>{e.onCleanup(o)}}}));
