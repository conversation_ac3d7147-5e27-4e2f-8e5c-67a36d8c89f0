{"name": "rate-limiter-flexible", "version": "5.0.5", "description": "Node.js rate limiter by key and protection from DDoS and Brute-Force attacks in process Memory, Redis, MongoDb, Memcached, MySQL, PostgreSQL, Cluster or PM", "main": "index.js", "scripts": {"dc:up": "docker-compose -f docker-compose.yml up -d", "dc:down": "docker-compose -f docker-compose.yml down", "prisma:postgres": "prisma generate --schema=./test/RateLimiterPrisma/Postgres/schema.prisma && prisma db push --schema=./test/RateLimiterPrisma/Postgres/schema.prisma", "test": "npm run prisma:postgres && nyc  --reporter=html --reporter=text mocha", "debug-test": "mocha --inspect-brk lib/**/**.test.js", "coveralls": "cat ./coverage/lcov.info | coveralls", "eslint": "eslint --quiet lib/**/**.js test/**/**.js", "eslint-fix": "eslint --fix lib/**/**.js test/**/**.js"}, "repository": {"type": "git", "url": "git+https://github.com/animir/node-rate-limiter-flexible.git"}, "keywords": ["ratelimter", "authorization", "security", "rate", "limit", "bruteforce", "throttle", "redis", "mongodb", "dynamodb", "mysql", "postgres", "prisma", "koa", "express", "hapi"], "author": "animir <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/animir/node-rate-limiter-flexible/issues"}, "homepage": "https://github.com/animir/node-rate-limiter-flexible#readme", "types": "./lib/index.d.ts", "devDependencies": {"@aws-sdk/client-dynamodb": "^3.431.0", "@prisma/client": "^5.8.0", "chai": "^4.1.2", "coveralls": "^3.0.1", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-security": "^1.4.0", "ioredis": "^5.3.2", "istanbul": "^1.1.0-alpha.1", "memcached-mock": "^0.1.0", "mocha": "^10.2.0", "nyc": "^15.1.0", "prisma": "^5.8.0", "redis": "^4.6.8", "redis-mock": "^0.48.0", "sinon": "^17.0.1"}, "browser": {"cluster": false, "crypto": false}}