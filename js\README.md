# 安全日志分析器 - 模块化架构

## 📁 项目结构

```
js/
├── main.js              # 主应用入口
├── config.js            # 配置管理模块
├── parser.js            # JSON解析器模块
├── preprocessor.js      # 安全日志预处理器模块
├── lmstudio-client.js   # LM Studio API客户端模块
├── damoxing.json        # 模型配置文件
├── demo.json            # 示例数据文件
└── README.md            # 本文档
```

## 🔧 模块说明

### 1. main.js - 主应用入口
- **SecurityLogAnalyzer** 类：主应用控制器
- 协调各个模块的工作
- 处理用户界面交互
- 管理应用生命周期

### 2. config.js - 配置管理模块
- **ConfigManager** 类：统一配置管理
- 加载和管理 `damoxing.json` 和 `demo.json`
- 提供配置访问接口
- 生成动态提示词

### 3. parser.js - JSON解析器模块
- **JSONParser** 类：专业JSON解析
- 支持标准JSON和对象字面量格式
- 智能类型识别和转换
- 详细的错误报告

### 4. preprocessor.js - 安全日志预处理器模块
- **SecurityLogPreprocessor** 类：安全日志预处理
- 提取关键安全字段
- 解析嵌套的message字段
- 格式化输出预处理结果

### 5. lmstudio-client.js - LM Studio API客户端模块
- **LMStudioClient** 类：LM Studio API封装
- 处理模型推理标签
- 连接测试功能
- 错误处理和重试机制

## 📄 配置文件

### damoxing.json - 模型配置
```json
{
  "lmStudio": {
    "baseURL": "http://localhost:1234",
    "model": "microsoft/phi-4-mini-reasoning",
    "systemPrompt": "...",
    "analysisPrompt": "..."
  },
  "preprocessing": {
    "criticalFields": {...}
  },
  "keywords": {
    "security": [...]
  }
}
```

### demo.json - 示例数据
```json
{
  "exampleSecurityLog": "..."
}
```

## 🚀 使用方式

1. **初始化**：应用启动时自动加载配置
2. **解析**：支持多种JSON格式的智能解析
3. **预处理**：自动提取关键安全字段
4. **分析**：调用LM Studio进行专业分析
5. **展示**：Markdown格式结果，支持一键复制

## 🔄 数据流

```
用户输入 → JSON解析 → 安全日志预处理 → LM Studio分析 → 结果展示
```

## ⚙️ 配置修改

- **修改模型**：编辑 `damoxing.json` 中的 `model` 字段
- **调整提示词**：修改 `analysisPrompt` 字段
- **更新关键词**：编辑 `keywords.security` 数组
- **修改示例**：更新 `demo.json` 文件

## 🛠️ 扩展性

模块化设计便于：
- 添加新的解析器
- 集成其他AI模型
- 扩展预处理功能
- 自定义输出格式
