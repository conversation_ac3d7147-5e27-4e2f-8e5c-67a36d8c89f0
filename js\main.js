import { create<PERSON>lova } from 'alova';
import { marked } from 'marked';

// 配置 Alova 实例，连接到本地 LM Studio
const alova = createAlova({
    baseURL: 'http://localhost:1234', // LM Studio 默认端口
    requestAdapter: fetch,
    responded: response => response.json()
});

// DOM 元素
const jsonInput = document.getElementById('jsonInput');
const parseBtn = document.getElementById('parseBtn');
const loading = document.getElementById('loading');
const error = document.getElementById('error');
const result = document.getElementById('result');

// 配置 marked 选项
marked.setOptions({
    breaks: true,
    gfm: true
});

// 主要功能类
class JSONParser {
    constructor() {
        this.initEventListeners();
    }

    initEventListeners() {
        parseBtn.addEventListener('click', () => this.handleParse());
        jsonInput.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.handleParse();
            }
        });
    }

    async handleParse() {
        const inputText = jsonInput.value.trim();
        
        if (!inputText) {
            this.showError('请输入要解析的 JSON 字符串');
            return;
        }

        // 验证 JSON 格式
        try {
            JSON.parse(inputText);
        } catch (e) {
            this.showError('输入的不是有效的 JSON 格式');
            return;
        }

        this.showLoading();
        
        try {
            const response = await this.callLMStudio(inputText);
            const processedContent = this.processMarkdown(response);
            this.showResult(processedContent);
        } catch (err) {
            this.showError(`请求失败: ${err.message}`);
        }
    }

    async callLMStudio(jsonString) {
        const prompt = `请分析以下 JSON 数据，并用 Markdown 格式返回详细的解析结果：

${jsonString}

请包含：
1. JSON 结构概述
2. 主要字段分析
3. 数据类型说明
4. 可能的用途或含义

请用中文回答，并使用适当的 Markdown 格式。`;

        const request = alova.Post('/v1/chat/completions', {
            model: "local-model", // 根据你的 LM Studio 模型调整
            messages: [
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 2000
        });

        const response = await request;
        return response.choices[0].message.content;
    }

    processMarkdown(content) {
        // 使用 marked 解析 Markdown
        let html = marked(content);
        
        // 处理特定开头的段落（以 "注意："、"重要："、"提示：" 开头）
        html = html.replace(
            /<p>(注意：|重要：|提示：|说明：)(.*?)<\/p>/g,
            '<p class="special-paragraph">$1$2</p>'
        );
        
        // 为文本内容添加下划线效果（关键词）
        html = this.addUnderlines(html);
        
        return html;
    }

    addUnderlines(html) {
        // 定义需要添加下划线的关键词
        const keywords = [
            'JSON', 'Object', 'Array', 'String', 'Number', 'Boolean', 'null',
            '对象', '数组', '字符串', '数字', '布尔值', '空值',
            '字段', '属性', '键值对', '数据类型', '结构'
        ];
        
        keywords.forEach(keyword => {
            const regex = new RegExp(`\\b(${keyword})\\b`, 'gi');
            html = html.replace(regex, '<span class="underlined">$1</span>');
        });
        
        return html;
    }

    showLoading() {
        this.hideAll();
        loading.classList.remove('hidden');
        parseBtn.disabled = true;
        parseBtn.textContent = '解析中...';
    }

    showError(message) {
        this.hideAll();
        error.textContent = message;
        error.classList.remove('hidden');
        this.resetButton();
    }

    showResult(content) {
        this.hideAll();
        result.innerHTML = content;
        result.classList.remove('hidden');
        this.resetButton();
    }

    hideAll() {
        loading.classList.add('hidden');
        error.classList.add('hidden');
        result.classList.add('hidden');
    }

    resetButton() {
        parseBtn.disabled = false;
        parseBtn.textContent = '解析 JSON';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new JSONParser();
});

// 添加一些示例数据的快捷按钮功能
window.loadExample = function() {
    const exampleJSON = {
        "user": {
            "id": 12345,
            "name": "张三",
            "email": "<EMAIL>",
            "age": 28,
            "isActive": true,
            "roles": ["user", "admin"],
            "profile": {
                "avatar": "https://example.com/avatar.jpg",
                "bio": "这是一个示例用户",
                "preferences": {
                    "theme": "dark",
                    "language": "zh-CN",
                    "notifications": true
                }
            },
            "lastLogin": "2024-01-15T10:30:00Z",
            "metadata": null
        }
    };
    
    jsonInput.value = JSON.stringify(exampleJSON, null, 2);
};
