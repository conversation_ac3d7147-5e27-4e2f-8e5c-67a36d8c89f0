import { marked } from 'marked';
import { configManager } from './config.js';
import { JSONParser } from './parser.js';
import { SecurityLogPreprocessor } from './preprocessor.js';
import { LMStudioClient } from './lmstudio-client.js';

// DOM 元素
const jsonInput = document.getElementById('jsonInput');
const parseBtn = document.getElementById('parseBtn');
const loading = document.getElementById('loading');
const error = document.getElementById('error');
const result = document.getElementById('result');

// 配置 marked 选项
marked.setOptions({
    breaks: true,
    gfm: true
});

// 主要应用类
class SecurityLogAnalyzer {
    constructor() {
        this.parser = new JSONParser();
        this.preprocessor = null;
        this.lmClient = null;
        this.keywords = [];
        this.init();
    }

    // 初始化应用
    async init() {
        try {
            // 加载配置
            await configManager.loadConfig();
            await configManager.loadDemoData();

            // 初始化组件
            const preprocessingConfig = configManager.getPreprocessingConfig();
            this.preprocessor = new SecurityLogPreprocessor(preprocessingConfig.criticalFields);
            this.lmClient = new LMStudioClient(configManager);
            this.keywords = configManager.getKeywords();

            // 初始化事件监听器
            this.initEventListeners();

            console.log('✅ 应用初始化完成');

        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.showError('应用初始化失败: ' + error.message);
        }
    }

    initEventListeners() {
        parseBtn.addEventListener('click', () => this.handleParse());
        jsonInput.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.handleParse();
            }
        });
    }

    async handleParse() {
        const inputText = jsonInput.value.trim();

        if (!inputText) {
            this.showError('请输入要解析的 JSON 字符串');
            return;
        }

        try {
            // 解析JSON
            console.log('🔍 开始解析JSON数据...');
            const rawLogData = this.parser.parse(inputText);

            // 预处理安全日志数据
            console.log('🔍 开始预处理安全日志数据...');
            const processedLog = this.preprocessor.preprocessSecurityLog(rawLogData);

            // 在终端打印预处理结果
            this.preprocessor.printProcessedLog(processedLog);

            this.showLoading();

            // 调用LM Studio进行分析
            const response = await this.lmClient.analyze(JSON.stringify(processedLog, null, 2));
            const processedContent = this.processStructuredResponse(response);
            this.showResult(processedContent);

        } catch (err) {
            console.error('处理失败:', err);
            this.showError(`处理失败: ${err.message}`);
        }
    }



    processStructuredResponse(content) {
        try {
            // 尝试提取JSON内容
            let jsonContent = content.trim();

            console.log('原始响应内容:', jsonContent);

            // 如果响应包含代码块，提取其中的JSON
            const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
            if (jsonMatch) {
                jsonContent = jsonMatch[1].trim();
                console.log('提取的JSON内容:', jsonContent);
            }

            // 尝试修复不完整的JSON
            jsonContent = this.fixIncompleteJSON(jsonContent);

            // 解析JSON
            const analysisResult = JSON.parse(jsonContent);

            // 转换为HTML格式
            const html = this.convertAnalysisToHTML(analysisResult);

            // 添加关键词下划线
            return this.addUnderlines(html);

        } catch (error) {
            console.warn('JSON解析失败，回退到Markdown处理:', error);
            console.log('失败的JSON内容:', jsonContent);
            // 如果JSON解析失败，回退到原来的Markdown处理
            return this.processMarkdown(content);
        }
    }

    fixIncompleteJSON(jsonStr) {
        let fixed = jsonStr.trim();

        // 处理不完整的属性名（没有值的情况）
        // 例如: "攻击技术分析" 应该变成 "攻击技术分析": {}
        fixed = fixed.replace(/"\s*$/, '": {}');
        fixed = fixed.replace(/"\s*\n\s*$/, '": {}');

        // 处理未闭合的字符串
        const quotes = (fixed.match(/"/g) || []).length;
        if (quotes % 2 !== 0) {
            // 找到最后一个未闭合的字符串并闭合它
            const lastQuoteIndex = fixed.lastIndexOf('"');
            if (lastQuoteIndex !== -1) {
                // 检查这个引号后面是否有内容需要闭合
                const afterQuote = fixed.substring(lastQuoteIndex + 1);
                if (!afterQuote.includes('"')) {
                    fixed += '"';
                }
            }
        }

        // 处理尾部逗号
        fixed = fixed.replace(/,\s*}/, '}');
        fixed = fixed.replace(/,\s*$/, '');

        // 计算并修复大括号平衡
        let openBraces = 0;
        let closeBraces = 0;

        for (let char of fixed) {
            if (char === '{') openBraces++;
            if (char === '}') closeBraces++;
        }

        const missingBraces = openBraces - closeBraces;
        if (missingBraces > 0) {
            fixed += '}'.repeat(missingBraces);
        }

        console.log('JSON修复过程:', {
            original: jsonStr.substring(0, 100) + '...',
            fixed: fixed.substring(0, 100) + '...',
            openBraces,
            closeBraces,
            missingBraces
        });

        return fixed;
    }

    convertAnalysisToHTML(analysisResult) {
        let html = '<div class="structured-analysis">';

        // 攻击路径分析
        if (analysisResult.攻击路径分析) {
            html += '<h2>🌐 攻击路径分析</h2>';
            html += '<div class="analysis-section">';

            const pathAnalysis = analysisResult.攻击路径分析;
            if (pathAnalysis.攻击源分析) {
                html += `<div class="analysis-item"><strong>攻击源分析：</strong>${pathAnalysis.攻击源分析}</div>`;
            }
            if (pathAnalysis.目标分析) {
                html += `<div class="analysis-item"><strong>目标分析：</strong>${pathAnalysis.目标分析}</div>`;
            }
            if (pathAnalysis.网络连接特征) {
                html += `<div class="analysis-item"><strong>网络连接特征：</strong>${pathAnalysis.网络连接特征}</div>`;
            }

            html += '</div>';
        }

        // 攻击技术分析
        if (analysisResult.攻击技术分析) {
            html += '<h2>⚔️ 攻击技术分析</h2>';
            html += '<div class="analysis-section">';

            const techAnalysis = analysisResult.攻击技术分析;
            if (techAnalysis.威胁分类) {
                html += `<div class="analysis-item threat-category"><strong>威胁分类：</strong><span class="category-tag">${techAnalysis.威胁分类}</span></div>`;
            }
            if (techAnalysis['MITRE_ATT&CK_映射']) {
                html += `<div class="analysis-item"><strong>MITRE ATT&CK 映射：</strong>${techAnalysis['MITRE_ATT&CK_映射']}</div>`;
            }
            if (techAnalysis.攻击载荷详情) {
                html += `<div class="analysis-item"><strong>攻击载荷详情：</strong>${techAnalysis.攻击载荷详情}</div>`;
            }
            if (techAnalysis.攻击手法说明) {
                html += `<div class="analysis-item"><strong>攻击手法说明：</strong>${techAnalysis.攻击手法说明}</div>`;
            }

            html += '</div>';
        }

        html += '</div>';
        return html;
    }

    processMarkdown(content) {
        // 使用 marked 解析 Markdown（回退方案）
        let html = marked(content);

        // 处理特定开头的段落
        html = html.replace(
            /<p>(注意：|重要：|提示：|说明：)(.*?)<\/p>/g,
            '<p class="special-paragraph">$1$2</p>'
        );

        return html;
    }

    addUnderlines(html) {
        // 使用配置中的关键词
        this.keywords.forEach(keyword => {
            const regex = new RegExp(`\\b(${keyword})\\b`, 'gi');
            html = html.replace(regex, '<span class="underlined">$1</span>');
        });

        return html;
    }

    showLoading() {
        this.hideAll();
        loading.classList.remove('hidden');
        parseBtn.disabled = true;
        parseBtn.textContent = '解析中...';
    }

    showError(message) {
        this.hideAll();
        error.textContent = message;
        error.classList.remove('hidden');
        this.resetButton();
    }

    showResult(content) {
        this.hideAll();

        // 创建复制按钮
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-btn';
        copyButton.innerHTML = '📋 一键复制';
        copyButton.onclick = () => this.copyToClipboard(content);

        // 创建结果容器
        const resultContainer = document.createElement('div');
        resultContainer.className = 'result-container';

        // 添加复制按钮
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'copy-button-container';
        buttonContainer.appendChild(copyButton);

        // 添加内容
        const contentDiv = document.createElement('div');
        contentDiv.className = 'result-content';
        contentDiv.innerHTML = content;

        resultContainer.appendChild(buttonContainer);
        resultContainer.appendChild(contentDiv);

        result.innerHTML = '';
        result.appendChild(resultContainer);
        result.classList.remove('hidden');
        this.resetButton();
    }

    async copyToClipboard(htmlContent) {
        try {
            // 创建临时元素来提取纯文本
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            await navigator.clipboard.writeText(textContent);

            // 显示复制成功提示
            const copyBtn = document.querySelector('.copy-btn');
            const originalText = copyBtn.innerHTML;
            copyBtn.innerHTML = '✅ 已复制';
            copyBtn.style.background = '#27ae60';

            setTimeout(() => {
                copyBtn.innerHTML = originalText;
                copyBtn.style.background = '';
            }, 2000);

        } catch (err) {
            console.error('复制失败:', err);
            alert('复制失败，请手动选择文本复制');
        }
    }

    hideAll() {
        loading.classList.add('hidden');
        error.classList.add('hidden');
        result.classList.add('hidden');
    }

    resetButton() {
        parseBtn.disabled = false;
        parseBtn.textContent = '解析 JSON';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new SecurityLogAnalyzer();
});

// 添加安全日志示例数据的快捷按钮功能
window.loadExample = async function() {
    try {
        if (!configManager.demoData) {
            await configManager.loadDemoData();
        }
        const exampleSecurityLog = configManager.getExampleSecurityLog();
        jsonInput.value = exampleSecurityLog;
        console.log('✅ 示例数据加载成功');
    } catch (error) {
        console.error('❌ 加载示例数据失败:', error);
        alert('加载示例数据失败: ' + error.message);
    }
};
