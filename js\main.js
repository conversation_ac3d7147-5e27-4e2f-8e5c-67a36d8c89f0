import { marked } from 'marked';
import { configManager } from './config.js';
import { JSONParser } from './parser.js';
import { SecurityLogPreprocessor } from './preprocessor.js';
import { LMStudioClient } from './lmstudio-client.js';

// DOM 元素
const jsonInput = document.getElementById('jsonInput');
const parseBtn = document.getElementById('parseBtn');
const loading = document.getElementById('loading');
const error = document.getElementById('error');
const result = document.getElementById('result');

// 配置 marked 选项
marked.setOptions({
    breaks: true,
    gfm: true
});

// 主要应用类
class SecurityLogAnalyzer {
    constructor() {
        this.parser = new JSONParser();
        this.preprocessor = null;
        this.lmClient = null;
        this.keywords = [];
        this.init();
    }

    // 初始化应用
    async init() {
        try {
            // 加载配置
            await configManager.loadConfig();
            await configManager.loadDemoData();

            // 初始化组件
            const preprocessingConfig = configManager.getPreprocessingConfig();
            this.preprocessor = new SecurityLogPreprocessor(preprocessingConfig.criticalFields);
            this.lmClient = new LMStudioClient(configManager);
            this.keywords = configManager.getKeywords();

            // 初始化事件监听器
            this.initEventListeners();

            console.log('✅ 应用初始化完成');

        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.showError('应用初始化失败: ' + error.message);
        }
    }

    initEventListeners() {
        parseBtn.addEventListener('click', () => this.handleParse());
        jsonInput.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.handleParse();
            }
        });
    }

    async handleParse() {
        const inputText = jsonInput.value.trim();

        if (!inputText) {
            this.showError('请输入要解析的 JSON 字符串');
            return;
        }

        try {
            // 解析JSON
            console.log('🔍 开始解析JSON数据...');
            const rawLogData = this.parser.parse(inputText);

            // 预处理安全日志数据
            console.log('🔍 开始预处理安全日志数据...');
            const processedLog = this.preprocessor.preprocessSecurityLog(rawLogData);

            // 在终端打印预处理结果
            this.preprocessor.printProcessedLog(processedLog);

            this.showLoading();

            // 调用LM Studio进行分析
            const response = await this.lmClient.analyze(JSON.stringify(processedLog, null, 2));
            const processedContent = this.processMarkdown(response);
            this.showResult(processedContent);

        } catch (err) {
            console.error('处理失败:', err);
            this.showError(`处理失败: ${err.message}`);
        }
    }



    processMarkdown(content) {
        // 使用 marked 解析 Markdown
        let html = marked(content);
        
        // 处理特定开头的段落（以 "注意："、"重要："、"提示：" 开头）
        html = html.replace(
            /<p>(注意：|重要：|提示：|说明：)(.*?)<\/p>/g,
            '<p class="special-paragraph">$1$2</p>'
        );
        
        // 为文本内容添加下划线效果（关键词）
        html = this.addUnderlines(html);
        
        return html;
    }

    addUnderlines(html) {
        // 使用配置中的关键词
        this.keywords.forEach(keyword => {
            const regex = new RegExp(`\\b(${keyword})\\b`, 'gi');
            html = html.replace(regex, '<span class="underlined">$1</span>');
        });

        return html;
    }

    showLoading() {
        this.hideAll();
        loading.classList.remove('hidden');
        parseBtn.disabled = true;
        parseBtn.textContent = '解析中...';
    }

    showError(message) {
        this.hideAll();
        error.textContent = message;
        error.classList.remove('hidden');
        this.resetButton();
    }

    showResult(content) {
        this.hideAll();

        // 创建复制按钮
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-btn';
        copyButton.innerHTML = '📋 一键复制';
        copyButton.onclick = () => this.copyToClipboard(content);

        // 创建结果容器
        const resultContainer = document.createElement('div');
        resultContainer.className = 'result-container';

        // 添加复制按钮
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'copy-button-container';
        buttonContainer.appendChild(copyButton);

        // 添加内容
        const contentDiv = document.createElement('div');
        contentDiv.className = 'result-content';
        contentDiv.innerHTML = content;

        resultContainer.appendChild(buttonContainer);
        resultContainer.appendChild(contentDiv);

        result.innerHTML = '';
        result.appendChild(resultContainer);
        result.classList.remove('hidden');
        this.resetButton();
    }

    async copyToClipboard(htmlContent) {
        try {
            // 创建临时元素来提取纯文本
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            await navigator.clipboard.writeText(textContent);

            // 显示复制成功提示
            const copyBtn = document.querySelector('.copy-btn');
            const originalText = copyBtn.innerHTML;
            copyBtn.innerHTML = '✅ 已复制';
            copyBtn.style.background = '#27ae60';

            setTimeout(() => {
                copyBtn.innerHTML = originalText;
                copyBtn.style.background = '';
            }, 2000);

        } catch (err) {
            console.error('复制失败:', err);
            alert('复制失败，请手动选择文本复制');
        }
    }

    hideAll() {
        loading.classList.add('hidden');
        error.classList.add('hidden');
        result.classList.add('hidden');
    }

    resetButton() {
        parseBtn.disabled = false;
        parseBtn.textContent = '解析 JSON';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new SecurityLogAnalyzer();
});

// 添加安全日志示例数据的快捷按钮功能
window.loadExample = async function() {
    try {
        if (!configManager.demoData) {
            await configManager.loadDemoData();
        }
        const exampleSecurityLog = configManager.getExampleSecurityLog();
        jsonInput.value = exampleSecurityLog;
        console.log('✅ 示例数据加载成功');
    } catch (error) {
        console.error('❌ 加载示例数据失败:', error);
        alert('加载示例数据失败: ' + error.message);
    }
};
