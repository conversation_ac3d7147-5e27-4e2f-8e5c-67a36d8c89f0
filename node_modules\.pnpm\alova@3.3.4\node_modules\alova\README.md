<img width="100%" src="https://alova.js.org/img/cover.jpg" />

<p align="center">
  alova is perfectly compatible with your favorite HTTP clients and UI frameworks, makes ultimate efficiency in APIs integration with its business modules and devtools.
</p>

<p align="center">English | <a href="./README.zh-CN.md">Chinese</a></p>

<p align="center">
  <a href="https://alova.js.org">Documentation</a> | 
  <a href="https://alova.js.org/examples">Deoms</a>
</p>

[![npm](https://img.shields.io/npm/v/alova)](https://www.npmjs.com/package/alova)
[![build](https://github.com/alovajs/alova/actions/workflows/release.yml/badge.svg?branch=main)](https://github.com/alovajs/alova/actions/workflows/release.yml)
[![coverage status](https://coveralls.io/repos/github/alovajs/alova/badge.svg?branch=main)](https://coveralls.io/github/alovajs/alova?branch=main)
[![stars](https://img.shields.io/github/stars/alovajs/alova?style=social)](https://github.com/alovajs/alova)
[![discord](https://img.shields.io/badge/chat-Discord-515ff1)](https://discord.gg/S47QGJgkVb)
[![wechat](https://img.shields.io/badge/chat_with_CH-Wechat-07c160)](https://alova.js.org/img/wechat_qrcode.jpg)
[![tree shaking](https://badgen.net/bundlephobia/tree-shaking/alova)](https://bundlephobia.com/package/alova)
![typescript](https://badgen.net/badge/icon/typescript?icon=typescript&label)
![license](https://img.shields.io/badge/license-MIT-blue.svg)

## Features

- ​​Easy to use, [watching video](https://alova.js.org/video-tutorial) to get started in 5 mins.
- ​​Full compatibility​​ with your favorite technology stack.
- ​​20+ high-performance business modules​​ for building faster apps.
- ​​Advanced OpenAPI solution​​ for efficient APIs information interaction within your code.
- Request sharing and response cache to improve app performance.
- Type safety.

## Is there any difference?

Unlike libraries such as `@tanstack/react-query`, `swrjs`, and `ahooks`'s `useRequest`, alova aims to make API integration very easy and efficient, while maintaining more efficient data interaction and bringing a smoother experience to users.

> You can also check [Comparison with other request libraries](https://alova.js.org/about/comparison) to learn more about the differences of alova.

## Join the community

- [Follow us on X](https://x.com/alovajs)

- [Join the Discord](https://discord.gg/S47QGJgkVb)

- [Join the WeChat group](https://alova.js.org/img/wechat_qrcode.jpg)

## We need your support

If you like alova, we are very grateful for giving us a star in the upper right corner, which is a recognition and encouragement for our work.

## Welcome to contribute

We are honored to receive active participation from developers around the world in Issues and Discussions.

We hope to make alova a common project for everyone who is willing to participate. We encourage everyone to become a contributor to the alova community with an open and inclusive attitude. Even if you are a junior developer, as long as your ideas meet the development guidelines of alova, please participate generously.

Effective contributions will win you a certain reputation in the Alova community. Before contributing, please be sure to read the [Contribution Guide](./CONTRIBUTING.zh-CN.md) in detail to ensure your contribution is effective.

## Changelog

[Link](https://github.com/alovajs/alova/releases)

## Contributors

<a href="https://github.com/alovajs/alova/graphs/contributors">
<img src="https://contrib.rocks/image?repo=alovajs/alova&max=30&columns=10" />
</a>

## LICENSE

[MIT](https://en.wikipedia.org/wiki/MIT_License)
