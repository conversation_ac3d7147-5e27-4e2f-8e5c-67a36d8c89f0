@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\augment-projects\k6 code\node_modules\.pnpm\marked@12.0.2\node_modules\marked\bin\node_modules;C:\Users\<USER>\Documents\augment-projects\k6 code\node_modules\.pnpm\marked@12.0.2\node_modules\marked\node_modules;C:\Users\<USER>\Documents\augment-projects\k6 code\node_modules\.pnpm\marked@12.0.2\node_modules;C:\Users\<USER>\Documents\augment-projects\k6 code\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\augment-projects\k6 code\node_modules\.pnpm\marked@12.0.2\node_modules\marked\bin\node_modules;C:\Users\<USER>\Documents\augment-projects\k6 code\node_modules\.pnpm\marked@12.0.2\node_modules\marked\node_modules;C:\Users\<USER>\Documents\augment-projects\k6 code\node_modules\.pnpm\marked@12.0.2\node_modules;C:\Users\<USER>\Documents\augment-projects\k6 code\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\marked.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\marked.js" %*
)
