{"lmStudio": {"baseURL": "http://localhost:1234", "endpoint": "/v1/chat/completions", "model": "moonlight-16b-a3b-instruct", "defaultParams": {"temperature": 0.7, "max_tokens": -1, "stream": false}, "systemPrompt": "你是一名专业的网络安全分析师，擅长分析安全告警日志并提供详细的威胁分析报告。", "analysisPrompt": "请作为网络安全专家分析以下安全告警日志，用 Markdown 格式返回简洁的分析报告：\n\n{logData}\n\n请只包含以下两个部分：\n\n## 🌐 攻击路径分析\n- 攻击源和目标分析\n- 网络连接特征\n\n## ⚔️ 攻击技术分析\n- MITRE ATT&CK 框架映射\n- 攻击载荷详情\n- 攻击手法说明\n\n要求：简洁明了，每个要点1-2句话，突出关键信息。"}, "preprocessing": {"criticalFields": {"attackInfo": ["logType", "classtype", "category", "severity", "severityLevel", "attackStatus", "attackStatusCn"], "networkInfo": ["srcIp", "srcPort", "dstIp", "dstPort", "proto", "appProto", "attacker", "victim"], "attackDetails": ["<PERSON><PERSON><PERSON><PERSON>", "killChainCn", "tacticId", "techniquesId"], "timeAndLocation": ["timestamp", "createTime", "srcIpCountry", "srcIpCity", "dstIpCountry", "dstIpCity"]}}, "keywords": {"security": ["目录遍历", "Directory Traversal", "SQL注入", "XSS", "CSRF", "RCE", "高危", "中危", "低危", "Critical", "High", "Medium", "Low", "成功", "失败", "可疑", "Success", "Failed", "Suspicious", "TCP", "UDP", "HTTP", "HTTPS", "DNS", "ICMP", "T1190", "TA0001", "Initial Access", "Exploitation", "美国", "中国", "俄罗斯", "朝鲜", "伊朗", "攻击者", "受害者", "载荷", "漏洞", "入侵", "恶意", "payload", "exploit", "malicious", "vulnerability", "IP", "端口", "Port", "源地址", "目标地址"]}}