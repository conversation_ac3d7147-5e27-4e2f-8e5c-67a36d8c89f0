{"lmStudio": {"baseURL": "http://localhost:1234", "endpoint": "/v1/chat/completions", "model": "moonlight-16b-a3b-instruct", "defaultParams": {"temperature": 0.7, "max_tokens": 1500, "stream": false}, "systemPrompt": "你是一名专业的网络安全分析师，擅长分析安全告警日志并提供详细的威胁分析报告。", "analysisPrompt": "请作为网络安全专家分析以下安全告警日志，严格按照以下JSON格式返回分析结果：\n\n{logData}\n\n请返回以下固定格式的JSON：\n\n```json\n{\n  \"攻击路径分析\": {\n    \"攻击源分析\": \"描述攻击来源IP、地理位置等信息\",\n    \"目标分析\": \"描述攻击目标IP、端口、服务等信息\",\n    \"网络连接特征\": \"描述协议、端口、连接方式等特征\"\n  },\n  \"攻击技术分析\": {\n    \"威胁分类\": \"从以下分类中选择：DDOS、探测扫描、漏洞利用、web攻击、账号异常、配置风险、行为审计、运维监控、恶意程序、主机异常、可疑通信、横向渗透、其他威胁\",\n    \"MITRE_ATT&CK_映射\": \"对应的战术ID和技术ID\",\n    \"攻击载荷详情\": \"具体的攻击载荷内容和特征\",\n    \"攻击手法说明\": \"攻击手法的详细说明\"\n  }\n}\n```\n\n要求：\n1. 严格按照上述JSON格式返回\n2. 威胁分类必须从指定的13个分类中选择\n3. 每个字段内容简洁明了，1-2句话\n4. 不要添加任何格式标记，只返回纯JSON"}, "preprocessing": {"criticalFields": {"attackInfo": ["logType", "classtype", "category", "severity", "severityLevel", "attackStatus", "attackStatusCn"], "networkInfo": ["srcIp", "srcPort", "dstIp", "dstPort", "proto", "appProto", "attacker", "victim"], "attackDetails": ["<PERSON><PERSON><PERSON><PERSON>", "killChainCn", "tacticId", "techniquesId"], "timeAndLocation": ["timestamp", "createTime", "srcIpCountry", "srcIpCity", "dstIpCountry", "dstIpCity"]}}, "threatCategories": ["DDOS", "探测扫描", "漏洞利用", "web攻击", "账号异常", "配置风险", "行为审计", "运维监控", "恶意程序", "主机异常", "可疑通信", "横向渗透", "其他威胁"], "keywords": {"security": ["高危", "中危", "低危", "成功", "失败", "可疑", "TCP", "HTTP", "HTTPS", "T1190", "TA0001", "攻击者", "受害者", "载荷", "漏洞", "IP", "端口"]}, "sessionConfig": {"enableHistory": true, "maxHistoryLength": 10, "autoSave": true, "cleanupDays": 7}}