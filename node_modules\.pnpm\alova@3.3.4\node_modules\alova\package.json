{"name": "<PERSON><PERSON>", "version": "3.3.4", "description": "The Request Toolkit For Ultimate Efficiency", "main": "dist/alova.common.cjs", "module": "dist/alova.esm.js", "types": "typings/index.d.ts", "type": "module", "sideEffects": false, "homepage": "https://alova.js.org", "keywords": ["react", "hooks", "xmlhttprequest", "v<PERSON><PERSON><PERSON>", "typescript", "vue", "reactjs", "svelte", "axios", "request", "fetch-api", "vue3", "react-hooks", "superagent", "<PERSON><PERSON>"], "jsdelivr": "dist/alova.umd.min.js", "unpkg": "dist/alova.umd.min.js", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/alovajs/alova.git"}, "bugs": {"url": "https://github.com/alovajs/alova/issues"}, "files": ["dist", "typings"], "engines": {"node": ">= 18.0.0"}, "exports": {".": {"types": "./typings/index.d.ts", "require": "./dist/alova.common.cjs", "default": "./dist/alova.esm.js"}, "./client": {"types": "./typings/clienthook/index.d.ts", "require": "./dist/clienthook/index.common.cjs", "default": "./dist/clienthook/index.esm.js"}, "./server": {"types": "./typings/serverhook.d.ts", "require": "./dist/serverhook/index.common.cjs", "default": "./dist/serverhook/index.esm.js"}, "./fetch": {"types": {"require": "./typings/fetch.d.cts", "default": "./typings/fetch.d.ts"}, "require": "./dist/adapter/fetch.common.cjs", "default": "./dist/adapter/fetch.esm.js"}, "./vue": {"types": "./typings/stateshook/vue.d.ts", "require": "./dist/stateshook/vue.common.cjs", "default": "./dist/stateshook/vue.esm.js"}, "./vue-demi": {"types": "./typings/stateshook/vue-demi.d.ts", "require": "./dist/stateshook/vue-demi.common.cjs", "default": "./dist/stateshook/vue-demi.esm.js"}, "./react": {"types": "./typings/stateshook/react.d.ts", "require": "./dist/stateshook/react.common.cjs", "default": "./dist/stateshook/react.esm.js"}, "./svelte": {"types": "./typings/stateshook/svelte.d.ts", "require": "./dist/stateshook/svelte.common.cjs", "default": "./dist/stateshook/svelte.esm.js"}, "./solid": {"types": "./typings/stateshook/solid.d.ts", "require": "./dist/stateshook/solid.common.cjs", "default": "./dist/stateshook/solid.esm.js"}, "./nuxt": {"types": "./typings/stateshook/nuxt.d.ts", "require": "./dist/stateshook/nuxt.common.cjs", "default": "./dist/stateshook/nuxt.esm.js"}}, "typesVersions": {"*": {"client": ["typings/clienthook/index.d.ts"], "server": ["typings/serverhook.d.ts"], "fetch": ["typings/fetch.d.ts"], "vue": ["typings/stateshook/vue.d.ts"], "vue-demi": ["typings/stateshook/vue-demi.d.ts"], "react": ["typings/stateshook/react.d.ts"], "svelte": ["typings/stateshook/svelte.d.ts"], "solid": ["typings/stateshook/solid.d.ts"], "nuxt": ["typings/stateshook/nuxt.d.ts"]}}, "dependencies": {"rate-limiter-flexible": "^5.0.3", "@alova/shared": "1.3.1"}, "devDependencies": {"@alova/scripts": "1.1.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "scripts": {"clean": "rimraf ./dist", "test": "vitest run", "test:coverage": "vitest run --coverage", "release": "semantic-release", "lint": "eslint --ext .ts,.js src", "lint:fix": "pnpm lint --fix", "build:core": "alova-scripts build core", "build:fetch": "alova-scripts build fetch", "build:client": "pnpm --filter=@alova/client run build", "build:server": "pnpm --filter=@alova/server run build", "build": "pnpm clean && alova-scripts build && pnpm build:client && pnpm build:server", "coveralls": "pnpm test:coverage && coveralls < coverage/lcov.info", "changelog": "conventional-changelog -p angular -u -i CHANGELOG.md -s -r 0", "commit": "git-cz && git push", "format": "prettier . --write"}}