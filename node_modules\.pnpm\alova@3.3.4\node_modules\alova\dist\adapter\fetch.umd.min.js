!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).alovaFetch=t()}(this,(function(){"use strict";const e="undefined",t=Promise,o=Object,n=!0,r=!1,a=e=>clearTimeout(e);typeof window===e&&typeof process!==e&&process.browser;const s=e=>{const t=(n=e,o.prototype.toString.call(n));var n;return/^\[object (Blob|FormData|ReadableStream|URLSearchParams)\]$/i.test(t)||((e,t)=>e instanceof t)(e,<PERSON><PERSON>yBuffer)},i=e=>"string"===(e=>typeof e)(e)||s(e);return function(){return(e,s)=>{const c=s.config,d=c.timeout||0,l=new AbortController,{data:f,headers:u}=e,p=/content-type/i.test(o.keys(u).join()),h=f&&"[object FormData]"===f.toString();p||h||(u["Content-Type"]="application/json;charset=UTF-8");const y=fetch(e.url,{...c,method:e.type,signal:l.signal,body:i(f)?f:(g=f,JSON.stringify(g,b,m))});var g,b,m;let w,j=r;return d>0&&(w=((e,t=0)=>setTimeout(e,t))((()=>{j=n,l.abort()}),d)),{response:()=>y.then((e=>(a(w),e.clone())),(e=>(e=>t.reject(e))(j?((e,...t)=>new e(...t))(Error,"fetchError: network timeout"):e))),headers:()=>y.then((({headers:e})=>e),(()=>({}))),onDownload:async e=>{let t=r;const o=await y.catch((()=>{t=n}));if(!o)return;const{headers:a,body:s}=o.clone(),i=s?s.getReader():undefined,c=Number(a.get("Content-Length")||a.get("content-length")||0);if(c<=0)return;let d=0;if(i){const o=()=>i.read().then((({done:n,value:r=new Uint8Array})=>{if(!n&&!t)return d+=r.byteLength,e(d,c),o();t&&e(d,0)}));o()}},onUpload(){console.error("fetch API does'nt support uploading progress. please consider to change `@alova/adapter-xhr` or `@alova/adapter-axios`")},abort:()=>{l.abort(),a(w)}}}}}));
