<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON 解析器 - LM Studio 集成</title>
    <link rel="stylesheet" href="./css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>JSON 解析器</h1>
            <p>使用 LM Studio 大模型解析 JSON 数据</p>
        </header>

        <main>
            <section class="input-section">
                <label for="jsonInput">输入 JSON 字符串：</label>
                <textarea 
                    id="jsonInput" 
                    placeholder="请输入要解析的 JSON 字符串..."
                    rows="8"
                ></textarea>
                <div class="button-group">
                    <button id="parseBtn" type="button">解析 JSON</button>
                    <button type="button" onclick="loadExample()" class="example-btn">加载示例</button>
                </div>
            </section>

            <section class="output-section">
                <h2>解析结果</h2>
                <div id="loading" class="loading hidden">正在解析中...</div>
                <div id="error" class="error hidden"></div>
                <div id="result" class="result"></div>
            </section>
        </main>
    </div>

    <script type="module" src="./js/main.js"></script>
</body>
</html>
