!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).alova={})}(this,(function(t){"use strict";const e="undefined",a=Promise,o=Object,s=RegExp,n=void 0,r=null,c=!0,i=!1,l=(t,e,a)=>t.then(e,a),h=(t,e)=>t.finally(e),u=(t,e,a)=>JSON.stringify(t,e,a),d=t=>o.keys(t),p=(t,e)=>t.forEach(e),f=(t,...e)=>t.push(...e),m=(t,e)=>t.map(e),y=(t,e)=>t.filter(e),g=t=>t.length,w=t=>Array.isArray(t),C=(t,e)=>delete t[e],b=t=>typeof t,v=typeof window===e&&(typeof process!==e?!process.browser:typeof Deno!==e),S="memory",$="restore",k=()=>{},x=t=>t,E=t=>"function"===b(t),D=t=>"string"===b(t),H=t=>o.prototype.toString.call(t),R=t=>"[object Object]"===H(t),T=(t,e)=>t instanceof e,U=t=>t?t.getTime():Date.now(),M=t=>t.context,O=t=>t.config,j=t=>t.options,P=t=>t.key,A=t=>{const{cacheFor:e}=O(t),a=t=>{return"number"!==b(e=t)||Number.isNaN(e)?U(t||n):U()+t;var e};let o=S,s=()=>0,r=i,c=n;const l=E(e);if(!l){let i=e;if(R(e)){const{mode:t=S,expire:a,tag:s}=e||{};o=t,r=t===$,c=s?s.toString():n,i=a}s=e=>a(E(i)?i({method:t,mode:e}):i)}return{f:e,c:l,e:s,m:o,s:r,t:c}},q=(t,...e)=>new t(...e),N=(t,e)=>"$a."+t+e,F=(t,e,a)=>{const o=/^https?:\/\//i.test(e);o||(t=t.endsWith("/")?t.slice(0,-1):t,""!==e&&(e=e.startsWith("/")?e:`/${e}`));const s=o?e:t+e,r=D(a)?a:m(y(d(a),(t=>a[t]!==n)),(t=>`${t}=${a[t]}`)).join("&");return r?+s.includes("?")?`${s}&${r}`:`${s}?${r}`:s},L=t=>{if(w(t))return m(t,L);if(R(t)&&t.constructor===o){const e={};return p(d(t),(a=>{e[a]=L(t[a])})),e}return t};class B extends Error{constructor(t,e,a){super(e+(a?`\n\nFor detailed: https://alova.js.org/error#${a}`:"")),this.name=`[alova${t?`/${t}`:""}]`}}const I=()=>{const t={};return{eventMap:t,on(e,a){const o=t[e]=t[e]||[];return f(o,a),()=>{t[e]=y(o,(t=>t!==a))}},off(e,a){const o=t[e];if(o)if(a){const t=o.indexOf(a);t>-1&&o.splice(t,1)}else delete t[e]},emit(e,a){const o=t[e]||[];return m(o,(t=>t(a)))}}};t.globalConfigMap={autoHitCache:"global",ssr:v};const _="color: black; font-size: 12px; font-weight: bolder";var G=(e,a,o,s)=>{const n=console,r=(...t)=>console.log(...t),{url:c}=a,i=o===$,l="[42m%s[49m",h="[32m%s[39m",u=` [HitCache]${c} `,d=()=>Array(g(u)+1).join("^");t.globalConfigMap.ssr?(r(l,u),r(h," Cache ",e),r(h," Mode  ",o),i&&r(h," Tag   ",s),r(h,d())):(n.groupCollapsed?n.groupCollapsed("%cHitCache","padding: 2px 6px; background: #c4fcd3; color: #53b56d;",c):r(l,u),r("%c[Cache]",_,e),r("%c[Mode]",_,o),i&&r("%c[Tag]",_,s),r("%c[Method]",_,a),n.groupEnd?n.groupEnd():r(h,d()))};const J=t=>`hss.${t}`,K="hsr.",W=t=>K+t,z="$$hsrs",Q="__$<>$__",V=(t,e)=>{t[e]=0},X=async(t,e,o,r,c,i,l)=>{if(r>U()&&o){const h=N(t,e);if(await c.set(h,y([o,r===1/0?n:r,l],Boolean)),i){const t={},e=[];p(i,(a=>{const o=T(a,s),n=o?a.source+(a.flags?Q+a.flags:""):a;n&&(o&&!t[n]&&f(e,n),V(t,o?W(n):J(n)))}));const o=m(d(t),(async t=>{const e=await c.get(t)||{};V(e,h),await c.set(t,e)})),n=async()=>{if(g(e)){const t=await c.get(z)||[];f(t,...e),await c.set(z,t)}};await a.all([...o,n()])}}},Y=async(t,e,a)=>{const o=N(t,e);await a.remove(o)},Z=async(t,e,a,o)=>{const s=await a.get(N(t,e));if(s){const[n,r,c]=s;if(c===o&&(!r||r>U()))return s;await Y(t,e,a)}},tt=async(t,e,a,o)=>{const s=await Z(t,e,a,o);return s?s[0]:n},et=async t=>a.all(t.map((t=>t.clear())));var at=t=>{const{data:e,config:a}=t,s={...a},{headers:n={},params:r={}}=s,c=M(t);s.headers={...n},s.params=D(r)?r:{...r};return((t,...e)=>o.assign(t,...e))(q(ct,t.type,c,t.url,s,e),{...t,config:s})};const ot=async e=>{const{autoHitCache:o}=t.globalConfigMap,{l1Cache:n,l2Cache:r}=M(e),c=P(e),{name:i}=O(e),l={global:[...wt,...Ct],self:[n,r],close:[]}[o];l&&g(l)&&await a.all(m(l,(t=>(async(t,e,o)=>{const n=`${e}`,r={},c=J(t);let i;if(r[c]=await o.get(c),e){const t=J(n);r[t]=await o.get(t),i=await o.get(z);const e=[];i&&g(i)&&(p(i,(t=>{const[a,o]=t.split(Q);q(s,a,o).test(n)&&f(e,t)})),await a.all(m(e,(async t=>{const e=W(t);r[e]=await o.get(e)}))))}const l=async t=>{try{await o.remove(t);for(const e in r){const a=r[e];a&&C(a,t)}}catch(t){}},h={};await a.all(m(d(r),(async t=>{const e=r[t];if(e){const t=[];for(const a in e)h[a]||(V(h,a),f(t,l(a)));await a.all(t)}})));const u=g(i||[]);await a.all(m(d(r),(async t=>{const e=r[t];e&&(g(d(e))?await o.set(t,e):(await o.remove(t),t.includes(K)&&i&&(i=y(i,(e=>W(e)!==t)))))}))),u!==g(i||[])&&await o.set(z,i)})(c,i,t))))},st={};function nt(t,e){let o,s=c;const u=q(a,(t=>{o=t}));return{abort:()=>{l(u,(t=>t&&t.abort()))},onDownload:t=>{l(u,(e=>e&&e.onDownload&&e.onDownload(t)))},onUpload:t=>{l(u,(e=>e&&e.onUpload&&e.onUpload(t)))},response:async()=>{const{beforeRequest:u=k,responded:d,requestAdapter:p,cacheLogger:f}=(t=>j(M(t)))(t),m=P(t),{s:y,t:g,m:w,e:b}=A(t),{id:v,l1Cache:D,l2Cache:U,snapshots:q}=M(t),{cacheFor:N}=O(t),{hitSource:B}=t;let I=await(E(N)?N():e?n:tt(v,m,D));if(w===$&&!I&&!e){const t=await Z(v,m,U,g);if(t){const[e,a]=t;await X(v,m,e,a,D,B),I=e}}const _=at(t);await u(_);const{baseURL:J,url:K,type:W,data:z}=_,{params:Q={},headers:V={},transform:Y=x,shareRequest:et}=O(_),nt=st[v]=st[v]||{},rt=_.data,ct=(t=>{const e=H(t);return/^\[object (Blob|FormData|ReadableStream|URLSearchParams)\]$/i.test(e)||T(t,ArrayBuffer)})(rt);let it=ct?n:nt[m],lt=x,ht=n,ut=k;if(E(d))lt=d;else if(R(d)){const{onSuccess:t,onError:e,onComplete:a}=d;lt=E(t)?t:lt,ht=E(e)?e:ht,ut=E(a)?a:ut}if(I!==n)return o(),_.fromCache=c,(pt=G,E(dt=f)?dt:[i,r].includes(dt)?k:pt)(I,_,w,g),ut(_),I;var dt,pt;if(s=i,!et||!it){const t=p({url:F(J,K,Q),type:W,data:z,headers:V},_);it=nt[m]=t}o(it);const ft=async(e,o,s=true)=>{const n=await e,r=await Y(n,o||{});q.save(t);try{await ot(_)}catch(t){}if((!rt||!ct)&&s)try{await a.all([X(v,m,r,b(S),D,B),y&&X(v,m,r,b($),U,B,g)])}catch(t){}return L(r)};return h(l(a.all([it.response(),it.headers()]),(([t,e])=>(C(nt,m),ft(lt(t,_),e))),(t=>{return C(nt,m),E(ht)?ft(ht(t,_),n,i):(e=t,a.reject(e));var e})),(()=>{ut(_)}))},fromCache:()=>s}}const rt=(t,e)=>()=>{const a=e.indexOf(t);a>=0&&e.splice(a,1)};class ct{constructor(t,e,a,o,s){this.dhs=[],this.uhs=[],this.fromCache=n;const r=()=>{r.a()};r.a=k,t=t.toUpperCase();const c=this,i=j(e);c.abort=r,c.baseURL=i.baseURL||"",c.url=a,c.type=t,c.context=e;const l={},h="cacheFor",u=R(i[h])?i[h][t]:n,d=o&&o.hitSource;p(["timeout","shareRequest"],(t=>{i[t]!==n&&(l[t]=i[t])})),u!==n&&(l[h]=u),d&&(c.hitSource=m(w(d)?d:[d],(t=>T(t,ct)?P(t):t)),C(o,"hitSource")),c.config={...l,headers:{},params:{},...o||{}},c.data=s,c.meta=o?o.meta:c.meta,c.key=c.generateKey()}onDownload(t){return f(this.dhs,t),rt(t,this.dhs)}onUpload(t){return f(this.uhs,t),rt(t,this.uhs)}send(t=i){const e=this,{response:a,onDownload:o,onUpload:s,abort:r,fromCache:c}=nt(e,t);return g(e.dhs)>0&&o(((t,a)=>p(e.dhs,(e=>e({loaded:t,total:a}))))),g(e.uhs)>0&&s(((t,a)=>p(e.uhs,(e=>e({loaded:t,total:a}))))),e.abort.a=r,e.fromCache=n,e.promise=l(a(),(t=>(e.fromCache=c(),t))),e.promise}setName(t){O(this).name=t}generateKey(){return(t=>{const{params:e,headers:a}=O(t);return u([t.type,t.url,e,t.data,a])})(this)}then(t,e){return l(this.send(),t,e)}catch(t){return((t,e)=>t.catch(e))(this.send(),t)}finally(t){return h(this.send(),t)}}const it=((t="")=>(e,a,o)=>{if(!e)throw q(B,t,a,o)})(),lt="success",ht=()=>{const t=I(),e=localStorage,a={set:(a,o)=>{e.setItem(a,u(o)),t.emit(lt,{type:"set",key:a,value:o,container:e})},get:a=>{const o=e.getItem(a),s=o?(t=>JSON.parse(t))(o):o;return t.emit(lt,{type:"get",key:a,value:s,container:e}),s},remove:a=>{e.removeItem(a),t.emit(lt,{type:"remove",key:a,container:e})},clear:()=>{e.clear(),t.emit(lt,{type:"clear",key:"",container:e})},emitter:t};return a},ut=Set;class dt{constructor(t){this.records={},this.occupy=0,it(t>=0,"expected snapshots limit to be >= 0"),this.capacity=t}save(t){const{name:e}=O(t),{records:a,occupy:o,capacity:s}=this;if(e&&o<s){(a[e]=a[e]||q(ut)).add(t),this.occupy+=1}}match(t,e=!0){let a,o,n,r=t;R(t)&&(r=t.name,n=t.filter),T(r,s)?o=r:D(r)&&(a=r);const{records:c}=this;let i=q(ut);a?i=c[a]||i:o&&p(y(d(c),(t=>o.test(t))),(t=>{c[t].forEach((t=>i.add(t)))}));const l=E(n)?y([...i],n):[...i];return e?l:l[0]}}const pt="GET",ft={cacheFor:{[pt]:3e5},shareRequest:c,snapshots:1e3};let mt=0;class yt{constructor(t){var e,a;const o=this;o.id=(t.id||(mt+=1)).toString(),o.l1Cache=t.l1Cache||(()=>{let t={};const e=I(),a={set(a,o){t[a]=o,e.emit(lt,{type:"set",key:a,value:o,container:t})},get:a=>{const o=t[a];return e.emit(lt,{type:"get",key:a,value:o,container:t}),o},remove(a){C(t,a),e.emit(lt,{type:"remove",key:a,container:t})},clear:()=>{t={},e.emit(lt,{type:"clear",key:"",container:t})},emitter:e};return a})(),o.l2Cache=t.l2Cache||("undefined"!=typeof localStorage?ht():(()=>{const t=()=>{it(i,"l2Cache is not defined.")};return{set:()=>{t()},get:()=>(t(),n),remove:()=>{t()},clear:()=>{}}})()),o.options={...ft,...t},o.snapshots=q(dt,null!==(a=null!==(e=t.snapshots)&&void 0!==e?e:ft.snapshots)&&void 0!==a?a:0)}Request(t){return q(ct,t.method||pt,this,t.url,t,t.data)}Get(t,e){return q(ct,pt,this,t,e)}Post(t,e,a){return q(ct,"POST",this,t,a,e)}Delete(t,e,a){return q(ct,"DELETE",this,t,a,e)}Put(t,e,a){return q(ct,"PUT",this,t,a,e)}Head(t,e){return q(ct,"HEAD",this,t,e)}Patch(t,e,a){return q(ct,"PATCH",this,t,a,e)}Options(t,e){return q(ct,"OPTIONS",this,t,e)}}let gt=n;const wt=[],Ct=[];t.Method=ct,t.createAlova=t=>{const e=q(yt,t),a=e.options.statesHook;gt&&a&&it(gt.name===a.name,"expected to use the same `statesHook`"),gt=a;const{l1Cache:o,l2Cache:s}=e;return!wt.includes(o)&&f(wt,o),!Ct.includes(s)&&f(Ct,s),e},t.globalConfig=e=>{t.globalConfigMap={...t.globalConfigMap,...e}},t.hitCacheBySource=ot,t.invalidateCache=async t=>{if(!t)return void await a.all([et(wt),et(Ct)]);const e=(w(t)?t:[t]).map((t=>{const{id:e,l1Cache:o,l2Cache:s}=M(t),{c:n,m:r}=A(t);if(n)return;const c=P(t);return a.all([Y(e,c,o),r===$?Y(e,c,s):a.resolve(i)]);var i}));await a.all(e)},t.promiseStatesHook=()=>(it(gt,"`statesHook` is not set in alova instance"),gt),t.queryCache=async(t,{policy:e="all"}={})=>{if(t&&t.key){const{id:a,l1Cache:o,l2Cache:s}=M(t),r=P(t),{f:c,c:i,s:l,e:h,t:u}=A(t);if(i)return c();let d="l2"!==e?await tt(a,r,o):n;return"l2"===e?d=await tt(a,r,s,u):"all"!==e||d||l&&h($)>U()&&(d=await tt(a,r,s,u)),d}},t.setCache=async(t,e,{policy:o="all"}={})=>{const s=(w(t)?t:[t]).map((async t=>{const{hitSource:s}=t,{id:r,l1Cache:c,l2Cache:i}=M(t),l=P(t),{e:h,s:u,t:d,c:p}=A(t);if(p)return;let f=e;if(E(e)){let t="l2"!==o?await tt(r,l,c):n;if(("l2"===o||"all"===o&&!t&&u&&h($)>U())&&(t=await tt(r,l,i,d)),f=e(t),f===n)return}return a.all(["l2"!==o&&X(r,l,f,h(S),c,s),"l2"===o||"all"===o&&u?X(r,l,f,h($),i,s,d):n])}));return a.all(s)}}));
