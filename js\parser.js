// JSON解析器模块
export class JSONParser {
    constructor() {
        // 解析器初始化
    }

    // 解析字符串值，处理转义字符
    parseStringValue(str) {
        // 移除首尾引号
        let content = str.slice(1, -1);
        
        // 处理转义字符
        content = content
            .replace(/\\"/g, '"')
            .replace(/\\n/g, '\n')
            .replace(/\\r/g, '\r')
            .replace(/\\t/g, '\t')
            .replace(/\\\\/g, '\\');
        
        return content;
    }

    // 专门解析对象字面量格式的方法
    parseObjectLiteral(input) {
        const text = input.trim();
        
        if (!text.startsWith('{') || !text.endsWith('}')) {
            throw new Error('输入必须以 { 开头，以 } 结尾');
        }

        // 移除首尾的大括号
        const content = text.slice(1, -1).trim();
        
        // 按行分割
        const lines = content.split('\n').map(line => line.trim()).filter(line => line);
        
        const result = {};
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            // 跳过空行
            if (!line) continue;
            
            // 查找冒号位置
            const colonIndex = line.indexOf(':');
            if (colonIndex === -1) {
                throw new Error(`第 ${i + 1} 行格式错误：缺少冒号 - ${line}`);
            }
            
            // 提取键名
            let key = line.substring(0, colonIndex).trim();
            
            // 提取值部分
            let valueStr = line.substring(colonIndex + 1).trim();
            
            // 解析值
            let value;
            
            if (valueStr.startsWith('"') && valueStr.endsWith('"')) {
                // 字符串值 - 需要处理内部的转义字符
                value = this.parseStringValue(valueStr);
            } else if (valueStr === 'null') {
                value = null;
            } else if (valueStr === 'true') {
                value = true;
            } else if (valueStr === 'false') {
                value = false;
            } else if (/^\d+$/.test(valueStr)) {
                // 纯数字
                value = parseInt(valueStr, 10);
            } else if (/^\d+\.\d+$/.test(valueStr)) {
                // 浮点数
                value = parseFloat(valueStr);
            } else if (valueStr === '[]') {
                // 空数组
                value = [];
            } else {
                // 其他情况当作字符串处理
                value = valueStr;
            }
            
            result[key] = value;
        }
        
        return result;
    }

    // 主解析方法
    parse(inputText) {
        let rawLogData;
        
        try {
            // 尝试直接解析标准JSON
            rawLogData = JSON.parse(inputText);
        } catch (e) {
            try {
                // 使用专门的解析器处理对象字面量格式
                rawLogData = this.parseObjectLiteral(inputText);
                console.log('✅ 成功解析对象字面量格式');
            } catch (e2) {
                console.error('JSON解析错误:', e2);
                throw new Error('输入格式错误：' + e2.message);
            }
        }
        
        return rawLogData;
    }
}
