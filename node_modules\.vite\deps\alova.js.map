{"version": 3, "sources": ["../../.pnpm/@alova+shared@1.3.1/node_modules/@alova/shared/dist/alova-shared.esm.js", "../../.pnpm/alova@3.3.4/node_modules/alova/dist/alova.esm.js"], "sourcesContent": ["/**\n  * @alova/shared 1.3.1 (https://alova.js.org)\n  * Document https://alova.js.org\n  * Copyright 2025 Scott Hu. All Rights Reserved\n  * Licensed under MIT (https://github.com/alovajs/alova/blob/main/LICENSE)\n*/\n\nconst undefStr = 'undefined';\n// The following unified processing functions or variables added to reduce the amount of compiled code\nconst PromiseCls = Promise;\nconst promiseResolve = (value) => PromiseCls.resolve(value);\nconst promiseReject = (value) => PromiseCls.reject(value);\nconst ObjectCls = Object;\nconst RegExpCls = RegExp;\nconst undefinedValue = undefined;\nconst nullValue = null;\nconst trueValue = true;\nconst falseValue = false;\nconst promiseThen = (promise, onFulfilled, onrejected) => promise.then(onFulfilled, onrejected);\nconst promiseCatch = (promise, onrejected) => promise.catch(onrejected);\nconst promiseFinally = (promise, onfinally) => promise.finally(onfinally);\nconst promiseAll = (values) => PromiseCls.all(values);\nconst JSONStringify = (value, replacer, space) => JSON.stringify(value, replacer, space);\nconst JSONParse = (value) => JSON.parse(value);\nconst setTimeoutFn = (fn, delay = 0) => setTimeout(fn, delay);\nconst clearTimeoutTimer = (timer) => clearTimeout(timer);\nconst objectKeys = (obj) => ObjectCls.keys(obj);\nconst objectValues = (obj) => ObjectCls.values(obj);\nconst forEach = (ary, fn) => ary.forEach(fn);\nconst pushItem = (ary, ...item) => ary.push(...item);\nconst mapItem = (ary, callbackfn) => ary.map(callbackfn);\nconst filterItem = (ary, predicate) => ary.filter(predicate);\nconst shift = (ary) => ary.shift();\nconst slice = (ary, start, end) => ary.slice(start, end);\nconst splice = (ary, start, deleteCount = 0, ...items) => ary.splice(start, deleteCount, ...items);\nconst len = (data) => data.length;\nconst isArray = (arg) => Array.isArray(arg);\nconst deleteAttr = (arg, attr) => delete arg[attr];\nconst typeOf = (arg) => typeof arg;\nconst regexpTest = (reg, str) => reg.test(`${str}`);\nconst includes = (ary, target) => ary.includes(target);\nconst valueObject = (value, writable = falseValue) => ({ value, writable });\nconst defineProperty = (o, key, value, isDescriptor = falseValue) => ObjectCls.defineProperty(o, key, isDescriptor ? value : valueObject(value, falseValue));\n// Whether it is running on the server side, node and bun are judged by process, and deno is judged by Deno.\n// Some frameworks (such as Alipay and uniapp) will inject the process object as a global variable which `browser` is true\nconst isSSR = typeof window === undefStr && (typeof process !== undefStr ? !process.browser : typeof Deno !== undefStr);\n/** cache mode */\n// only cache in memory, it's default option\nconst MEMORY = 'memory';\n// persistent cache, and will be read to memory when page is refreshed, it means that the memory cache always exist until cache is expired.\nconst STORAGE_RESTORE = 'restore';\n\n/**\n * Empty function for compatibility processing\n */\nconst noop = () => { };\n/**\n * A function that returns the parameter itself, used for compatibility processing\n * Since some systems use self as a reserved word, $self is used to distinguish it.\n * @param arg any parameter\n * @returns return parameter itself\n */\nconst $self = (arg) => arg;\n/**\n * Determine whether the parameter is a function any parameter\n * @returns Whether the parameter is a function\n */\nconst isFn = (arg) => typeOf(arg) === 'function';\n/**\n * Determine whether the parameter is a number any parameter\n * @returns Whether the parameter is a number\n */\nconst isNumber = (arg) => typeOf(arg) === 'number' && !Number.isNaN(arg);\n/**\n * Determine whether the parameter is a string any parameter\n * @returns Whether the parameter is a string\n */\nconst isString = (arg) => typeOf(arg) === 'string';\n/**\n * Determine whether the parameter is an object any parameter\n * @returns Whether the parameter is an object\n */\nconst isObject = (arg) => arg !== nullValue && typeOf(arg) === 'object';\n/**\n * Global toString any parameter stringified parameters\n */\nconst globalToString = (arg) => ObjectCls.prototype.toString.call(arg);\n/**\n * Determine whether it is a normal object any parameter\n * @returns Judgment result\n */\nconst isPlainObject = (arg) => globalToString(arg) === '[object Object]';\n/**\n * Determine whether it is an instance of a certain class any parameter\n * @returns Judgment result\n */\nconst instanceOf = (arg, cls) => arg instanceof cls;\n/**\n * Unified timestamp acquisition function\n * @returns Timestamp\n */\nconst getTime = (date) => (date ? date.getTime() : Date.now());\n/**\n * Get the alova instance through the method instance alova example\n */\nconst getContext = (methodInstance) => methodInstance.context;\n/**\n * Get method instance configuration data\n * @returns Configuration object\n */\nconst getConfig = (methodInstance) => methodInstance.config;\n/**\n * Get alova configuration data alova configuration object\n */\nconst getContextOptions = (alovaInstance) => alovaInstance.options;\n/**\n * Get alova configuration data through method instance alova configuration object\n */\nconst getOptions = (methodInstance) => getContextOptions(getContext(methodInstance));\n/**\n * Get the key value of the request method\n * @returns The key value of this request method\n */\nconst key = (methodInstance) => {\n    const { params, headers } = getConfig(methodInstance);\n    return JSONStringify([methodInstance.type, methodInstance.url, params, methodInstance.data, headers]);\n};\n/**\n * Create uuid simple version uuid\n */\nconst uuid = () => {\n    const timestamp = new Date().getTime();\n    return Math.floor(Math.random() * timestamp).toString(36);\n};\n/**\n * Get the key value of the method instance method instance\n * @returns The key value of this method instance\n */\nconst getMethodInternalKey = (methodInstance) => methodInstance.key;\n/**\n * Get the request method object\n * @param methodHandler Request method handle\n * @param args Method call parameters request method object\n */\nconst getHandlerMethod = (methodHandler, assert, args = []) => {\n    const methodInstance = isFn(methodHandler) ? methodHandler(...args) : methodHandler;\n    assert(!!methodInstance.key, 'hook handler must be a method instance or a function that returns method instance');\n    return methodInstance;\n};\n/**\n * Is it special data\n * @param data Submit data\n * @returns Judgment result\n */\nconst isSpecialRequestBody = (data) => {\n    const dataTypeString = globalToString(data);\n    return (/^\\[object (Blob|FormData|ReadableStream|URLSearchParams)\\]$/i.test(dataTypeString) || instanceOf(data, ArrayBuffer));\n};\nconst objAssign = (target, ...sources) => ObjectCls.assign(target, ...sources);\n/**\n * Excludes specified attributes from a data collection and returns a new data collection data collection\n * @param keys Excluded keys new data collection\n */\nconst omit = (obj, ...keys) => {\n    const result = {};\n    for (const key in obj) {\n        if (!keys.includes(key)) {\n            result[key] = obj[key];\n        }\n    }\n    return result;\n};\n/**\n * the same as `Promise.withResolvers`\n * @returns promise with resolvers.\n */\nfunction usePromise() {\n    let retResolve;\n    let retReject;\n    const promise = new Promise((resolve, reject) => {\n        retResolve = resolve;\n        retReject = reject;\n    });\n    return { promise, resolve: retResolve, reject: retReject };\n}\n/**\n * Get cached configuration parameters, fixedly returning an object in the format { e: function, c: any, f: any, m: number, s: boolean, t: string } e is the abbreviation of expire, which returns the cache expiration time point (timestamp) in milliseconds.\n * c is controlled, indicating whether it is a controlled cache\n * f is the original value of cacheFor, which is used to call to obtain cached data when c is true.\n * m is the abbreviation of mode, storage mode\n * s is the abbreviation of storage, whether to store it locally\n * t is the abbreviation of tag, which stores tags persistently.\n * @param methodInstance method instance\n * @returns Unified cache parameter object\n */\nconst getLocalCacheConfigParam = (methodInstance) => {\n    const { cacheFor } = getConfig(methodInstance);\n    const getCacheExpireTs = (cacheExpire) => isNumber(cacheExpire) ? getTime() + cacheExpire : getTime(cacheExpire || undefinedValue);\n    let cacheMode = MEMORY;\n    let expire = () => 0;\n    let store = falseValue;\n    let tag = undefinedValue;\n    const controlled = isFn(cacheFor);\n    if (!controlled) {\n        let expireColumn = cacheFor;\n        if (isPlainObject(cacheFor)) {\n            const { mode = MEMORY, expire, tag: configTag } = cacheFor || {};\n            cacheMode = mode;\n            store = mode === STORAGE_RESTORE;\n            tag = configTag ? configTag.toString() : undefinedValue;\n            expireColumn = expire;\n        }\n        expire = (mode) => getCacheExpireTs(isFn(expireColumn) ? expireColumn({ method: methodInstance, mode }) : expireColumn);\n    }\n    return {\n        f: cacheFor,\n        c: controlled,\n        e: expire,\n        m: cacheMode,\n        s: store,\n        t: tag\n    };\n};\n/**\n * Create class instance\n * @param Cls Constructor\n * @param args Constructor parameters class instance\n */\nconst newInstance = (Cls, ...args) => new Cls(...args);\n/**\n * Unified configuration\n * @param data\n * @returns unified configuration\n */\nconst sloughConfig = (config, args = []) => isFn(config) ? config(...args) : config;\nconst sloughFunction = (arg, defaultFn) => isFn(arg) ? arg : ![falseValue, nullValue].includes(arg) ? defaultFn : noop;\n/**\n * Create an executor that calls multiple times synchronously and only executes it once asynchronously\n */\nconst createSyncOnceRunner = (delay = 0) => {\n    let timer = undefinedValue;\n    // Executing multiple calls to this function will execute once asynchronously\n    return (fn) => {\n        if (timer) {\n            clearTimeout(timer);\n        }\n        timer = setTimeoutFn(fn, delay);\n    };\n};\n/**\n * Create an asynchronous function queue, the asynchronous function will be executed serially queue add function\n */\nconst createAsyncQueue = (catchError = falseValue) => {\n    const queue = [];\n    let completedHandler = undefinedValue;\n    let executing = false;\n    const executeQueue = async () => {\n        executing = trueValue;\n        while (len(queue) > 0) {\n            const asyncFunc = shift(queue);\n            if (asyncFunc) {\n                await asyncFunc();\n            }\n        }\n        completedHandler && completedHandler();\n        executing = falseValue;\n    };\n    const addQueue = (asyncFunc) => newInstance((PromiseCls), (resolve, reject) => {\n        const wrappedFunc = () => promiseThen(asyncFunc(), resolve, err => {\n            catchError ? resolve(undefinedValue) : reject(err);\n        });\n        pushItem(queue, wrappedFunc);\n        if (!executing) {\n            executeQueue();\n        }\n    });\n    const onComplete = (fn) => {\n        completedHandler = fn;\n    };\n    return {\n        addQueue,\n        onComplete\n    };\n};\n/**\n * Traverse the target object deeply target audience\n * @param callback Traversal callback\n * @param preorder Whether to traverse in preorder, the default is true\n * @param key The currently traversed key\n * @param parent The parent node currently traversed\n */\nconst walkObject = (target, callback, preorder = trueValue, key, parent) => {\n    const callCallback = () => {\n        if (parent && key) {\n            target = callback(target, key, parent);\n            if (target !== parent[key]) {\n                parent[key] = target;\n            }\n        }\n    };\n    // Preorder traversal\n    preorder && callCallback();\n    if (isObject(target)) {\n        for (const i in target) {\n            if (!instanceOf(target, String)) {\n                walkObject(target[i], callback, preorder, i, target);\n            }\n        }\n    }\n    // Postal order traversal\n    !preorder && callCallback();\n    return target;\n};\nconst cacheKeyPrefix = '$a.';\n/**\n * build common cache key.\n */\nconst buildNamespacedCacheKey = (namespace, key) => cacheKeyPrefix + namespace + key;\n/**\n * Calculate retry delay time based on avoidance strategy and number of retries avoid parameters\n * @param retryTimes Number of retries\n * @returns Retry delay time\n */\nconst delayWithBackoff = (backoff, retryTimes) => {\n    let { startQuiver, endQuiver } = backoff;\n    const { delay, multiplier = 1 } = backoff;\n    let retryDelayFinally = (delay || 0) * multiplier ** (retryTimes - 1);\n    // If start quiver or end quiver has a value, you need to increase the random jitter value in the specified range\n    if (startQuiver || endQuiver) {\n        startQuiver = startQuiver || 0;\n        endQuiver = endQuiver || 1;\n        retryDelayFinally +=\n            retryDelayFinally * startQuiver + Math.random() * retryDelayFinally * (endQuiver - startQuiver);\n        retryDelayFinally = Math.floor(retryDelayFinally); // round delay\n    }\n    return retryDelayFinally;\n};\n/**\n * Build the complete url baseURL path url parameters complete url\n */\nconst buildCompletedURL = (baseURL, url, params) => {\n    // Check if the URL starts with http/https\n    const startsWithPrefix = /^https?:\\/\\//i.test(url);\n    if (!startsWithPrefix) {\n        // If the Base url ends with /, remove /\n        baseURL = baseURL.endsWith('/') ? baseURL.slice(0, -1) : baseURL;\n        // If it does not start with /or http protocol, you need to add /\n        // Compatible with some RESTful usage fix: https://github.com/alovajs/alova/issues/382\n        if (url !== '') {\n            // Since absolute URLs (http/https) are handled above,\n            // we only need to ensure relative URLs start with a forward slash\n            url = url.startsWith('/') ? url : `/${url}`;\n        }\n    }\n    // fix: https://github.com/alovajs/alova/issues/653\n    const completeURL = startsWithPrefix ? url : baseURL + url;\n    // Convert params object to get string\n    // Filter out those whose value is undefined\n    const paramsStr = isString(params)\n        ? params\n        : mapItem(filterItem(objectKeys(params), key => params[key] !== undefinedValue), key => `${key}=${params[key]}`).join('&');\n    // Splice the get parameters behind the url. Note that the url may already have parameters.\n    return paramsStr\n        ? +completeURL.includes('?')\n            ? `${completeURL}&${paramsStr}`\n            : `${completeURL}?${paramsStr}`\n        : completeURL;\n};\n/**\n * Deep clone an object.\n *\n * @param obj The object to be cloned.\n * @returns The cloned object.\n */\nconst deepClone = (obj) => {\n    if (isArray(obj)) {\n        return mapItem(obj, deepClone);\n    }\n    if (isPlainObject(obj) && obj.constructor === ObjectCls) {\n        const clone = {};\n        forEach(objectKeys(obj), key => {\n            clone[key] = deepClone(obj[key]);\n        });\n        return clone;\n    }\n    return obj;\n};\n\n/**\n * alova error class\n */\nclass AlovaError extends Error {\n    constructor(prefix, message, errorCode) {\n        super(message + (errorCode ? `\\n\\nFor detailed: https://alova.js.org/error#${errorCode}` : ''));\n        this.name = `[alova${prefix ? `/${prefix}` : ''}]`;\n    }\n}\n/**\n * Custom assertion function that throws an error when the expression is false\n * When errorCode is passed in, a link to the error document will be provided to guide the user to correct it.\n * @param expression Judgment expression, true or false\n * @param message Assert message\n */\nconst createAssert = (prefix = '') => (expression, message, errorCode) => {\n    if (!expression) {\n        throw newInstance(AlovaError, prefix, message, errorCode);\n    }\n};\n\nconst bridgeObject = JSON.parse;\n/**\n * Injects a reference object with `JSON.parse` so that it can be accessed in another module.\n * @param object injecting object\n */\nconst provideReferingObject = (object) => {\n    bridgeObject.bridgeData = object;\n};\nconst injectReferingObject = () => (bridgeObject.bridgeData || {});\n\nconst createEventManager = () => {\n    const eventMap = {};\n    return {\n        eventMap,\n        on(type, handler) {\n            const eventTypeItem = (eventMap[type] = eventMap[type] || []);\n            pushItem(eventTypeItem, handler);\n            // return the off function\n            return () => {\n                eventMap[type] = filterItem(eventTypeItem, item => item !== handler);\n            };\n        },\n        off(type, handler) {\n            const handlers = eventMap[type];\n            if (!handlers) {\n                return;\n            }\n            if (handler) {\n                const index = handlers.indexOf(handler);\n                index > -1 && handlers.splice(index, 1);\n            }\n            else {\n                delete eventMap[type];\n            }\n        },\n        emit(type, event) {\n            const handlers = eventMap[type] || [];\n            return mapItem(handlers, handler => handler(event));\n        }\n    };\n};\nconst decorateEvent = (onEvent, decoratedHandler) => {\n    const emitter = createEventManager();\n    const eventType = uuid();\n    const eventReturn = onEvent(event => emitter.emit(eventType, event));\n    return (handler) => {\n        emitter.on(eventType, event => {\n            decoratedHandler(handler, event);\n        });\n        return eventReturn;\n    };\n};\n\nclass FrameworkReadableState {\n    constructor(state, key, dehydrate, exportState) {\n        this.s = state;\n        this.k = key;\n        this.$dhy = dehydrate;\n        this.$exp = exportState;\n    }\n    get v() {\n        return this.$dhy(this.s);\n    }\n    get e() {\n        return this.$exp(this.s);\n    }\n}\nclass FrameworkState extends FrameworkReadableState {\n    constructor(state, key, dehydrate, exportState, update) {\n        super(state, key, dehydrate, exportState);\n        this.$upd = update;\n    }\n    set v(newValue) {\n        this.$upd(this.s, newValue);\n    }\n    get v() {\n        return this.$dhy(this.s);\n    }\n}\n\nclass QueueCallback {\n    /**\n     * @param [limit=null] no limit if set undefined or null\n     * @param [initialProcessing=false]\n     */\n    constructor(limit, initialProcessing = false) {\n        this.limit = limit;\n        this.callbackQueue = [];\n        this.isProcessing = false;\n        this.interrupt = false;\n        this.isProcessing = initialProcessing;\n    }\n    /**\n     * Adds a callback function to the callback queue.\n     * If a limit is set and the queue has reached its limit, the callback will not be added.\n     * @param callback The callback function to be added to the queue.\n     */\n    queueCallback(callback) {\n        if (this.limit && this.callbackQueue.length >= this.limit) {\n            return;\n        }\n        this.callbackQueue.push(callback);\n        if (!this.isProcessing) {\n            this.tryRunQueueCallback();\n        }\n    }\n    /**\n     * Tries to run the callbacks in the queue.\n     * If there are callbacks in the queue, it removes the first callback and executes it.\n     * This method is called recursively until there are no more callbacks in the queue.\n     */\n    async tryRunQueueCallback() {\n        this.isProcessing = true;\n        this.interrupt = false;\n        while (this.callbackQueue.length > 0 && !this.interrupt) {\n            const cb = this.callbackQueue.shift();\n            await (cb === null || cb === void 0 ? void 0 : cb());\n        }\n        this.isProcessing = false;\n    }\n    /**\n     * If set the param `state` to true, it will interrupt the current job (whether or not the current processing state is true)\n     * If set the param `state` to false, then get on with the rest of the work\n     */\n    setProcessingState(state) {\n        this.isProcessing = state;\n        if (!state) {\n            this.tryRunQueueCallback();\n        }\n        else {\n            this.interrupt = true;\n        }\n    }\n}\n\nconst type = {};\n\nexport { $self, AlovaError, FrameworkReadableState, FrameworkState, JSONParse, JSONStringify, MEMORY, ObjectCls, PromiseCls, QueueCallback, RegExpCls, STORAGE_RESTORE, buildCompletedURL, buildNamespacedCacheKey, clearTimeoutTimer, createAssert, createAsyncQueue, createEventManager, createSyncOnceRunner, decorateEvent, deepClone, defineProperty, delayWithBackoff, deleteAttr, falseValue, filterItem, forEach, getConfig, getContext, getContextOptions, getHandlerMethod, getLocalCacheConfigParam, getMethodInternalKey, getOptions, getTime, globalToString, includes, injectReferingObject, instanceOf, isArray, isFn, isNumber, isObject, isPlainObject, isSSR, isSpecialRequestBody, isString, key, len, mapItem, newInstance, noop, nullValue, objAssign, objectKeys, objectValues, omit, promiseAll, promiseCatch, promiseFinally, promiseReject, promiseResolve, promiseThen, provideReferingObject, pushItem, regexpTest, setTimeoutFn, shift, slice, sloughConfig, sloughFunction, splice, trueValue, type, typeOf, undefinedValue, usePromise, uuid, valueObject, walkObject };\n", "/**\n  * alova 3.3.4 (https://alova.js.org)\n  * Document https://alova.js.org\n  * Copyright 2025 Scott Hu. All Rights Reserved\n  * Licensed under MIT (https://github.com/alovajs/alova/blob/main/LICENSE)\n*/\n\nimport { isSSR, STORAGE_RESTORE, len, getTime, buildNamespacedCacheKey, filterItem, undefinedValue, forEach, instanceOf, RegExpCls, pushItem, mapItem, objectKeys, PromiseCls, newInstance, deleteAttr, getContext, isString, objAssign, getMethodInternalKey, getLocalCacheConfigParam, isArray, isFn, MEMORY, promiseResolve, getConfig, promiseThen, trueValue, getOptions, isSpecialRequestBody, isPlainObject, sloughFunction, falseValue, buildCompletedURL, promiseFinally, promiseReject, noop, $self, deepClone, getContextOptions, key, promiseCatch, createAssert, createEventManager, JSONStringify, JSONParse } from '@alova/shared';\n\nlet globalConfigMap = {\n    autoHitCache: 'global',\n    ssr: isSSR\n};\n/**\n * Set global configuration\n * @param config\n */\nvar globalConfig = (config) => {\n    globalConfigMap = {\n        ...globalConfigMap,\n        ...config\n    };\n};\n\nconst titleStyle = 'color: black; font-size: 12px; font-weight: bolder';\n/**\n * Default cacheLogger function\n */\nvar defaultCacheLogger = (response, methodInstance, cacheMode, tag) => {\n    const cole = console;\n    // eslint-disable-next-line\n    const log = (...args) => console.log(...args);\n    const { url } = methodInstance;\n    const isRestoreMode = cacheMode === STORAGE_RESTORE;\n    const hdStyle = '\\x1B[42m%s\\x1B[49m';\n    const labelStyle = '\\x1B[32m%s\\x1B[39m';\n    const startSep = ` [HitCache]${url} `;\n    const endSepFn = () => Array(len(startSep) + 1).join('^');\n    if (globalConfigMap.ssr) {\n        log(hdStyle, startSep);\n        log(labelStyle, ' Cache ', response);\n        log(labelStyle, ' Mode  ', cacheMode);\n        isRestoreMode && log(labelStyle, ' Tag   ', tag);\n        log(labelStyle, endSepFn());\n    }\n    else {\n        cole.groupCollapsed\n            ? cole.groupCollapsed('%cHitCache', 'padding: 2px 6px; background: #c4fcd3; color: #53b56d;', url)\n            : log(hdStyle, startSep);\n        log('%c[Cache]', titleStyle, response);\n        log('%c[Mode]', titleStyle, cacheMode);\n        isRestoreMode && log('%c[Tag]', titleStyle, tag);\n        log('%c[Method]', titleStyle, methodInstance);\n        cole.groupEnd ? cole.groupEnd() : log(labelStyle, endSepFn());\n    }\n};\n\nconst hitSourceStringCacheKey = (key) => `hss.${key}`;\nconst hitSourceRegexpPrefix = 'hsr.';\nconst hitSourceRegexpCacheKey = (regexpStr) => hitSourceRegexpPrefix + regexpStr;\nconst unifiedHitSourceRegexpCacheKey = '$$hsrs';\nconst regexpSourceFlagSeparator = '__$<>$__';\nconst addItem = (obj, item) => {\n    obj[item] = 0;\n};\n/**\n * set or update cache\n * @param namespace namespace\n * @param key stored key\n * @param response Stored response content\n * @param expireTimestamp Timestamp representation of expiration time point\n * @param storage storage object\n * @param tag Storage tags, used to distinguish different storage tags\n */\nconst setWithCacheAdapter = async (namespace, key, data, expireTimestamp, cacheAdapter, hitSource, tag) => {\n    // not to cache if expireTimestamp is less than current timestamp\n    if (expireTimestamp > getTime() && data) {\n        const methodCacheKey = buildNamespacedCacheKey(namespace, key);\n        await cacheAdapter.set(methodCacheKey, filterItem([data, expireTimestamp === Infinity ? undefinedValue : expireTimestamp, tag], Boolean));\n        // save the relationship between this method and its hitSources.\n        // cache structure is like this:\n        /*\n          {\n            \"$a.[namespace][methodKey]\": [cache data],\n            ...\n            \"hss.[sourceMethodKey]\": \"{\n              [targetMethodKey1]: 0,\n              [targetMethodKey2]: 0,\n              ...\n            }\",\n            \"hss.[sourceMethodName]\": \"{\n              [targetMethodKey3]: 0,\n              [targetMethodKey4]: 0,\n              ...\n            }\",\n            \"hsr.[sourceMethodNameRegexpSource]\": \"{\n              [targetMethodKey5]: 0,\n              [targetMethodKey6]: 0,\n              ...\n            }\",\n            \"hsr.regexp1\": [\"hss.key1\", \"hss.key2\"],\n            \"hsr.regexp2\": [\"hss.key1\", \"hss.key2\"]\n          }\n        */\n        if (hitSource) {\n            // filter repeat items and categorize the regexp, to prevent unnecessary cost of IO\n            const hitSourceKeys = {};\n            const hitSourceRegexpSources = [];\n            forEach(hitSource, sourceItem => {\n                const isRegexp = instanceOf(sourceItem, RegExpCls);\n                const targetHitSourceKey = isRegexp\n                    ? sourceItem.source + (sourceItem.flags ? regexpSourceFlagSeparator + sourceItem.flags : '')\n                    : sourceItem;\n                if (targetHitSourceKey) {\n                    if (isRegexp && !hitSourceKeys[targetHitSourceKey]) {\n                        pushItem(hitSourceRegexpSources, targetHitSourceKey);\n                    }\n                    addItem(hitSourceKeys, isRegexp ? hitSourceRegexpCacheKey(targetHitSourceKey) : hitSourceStringCacheKey(targetHitSourceKey));\n                }\n            });\n            // save the relationship. Minimize IO as much as possible\n            const promises = mapItem(objectKeys(hitSourceKeys), async (hitSourceKey) => {\n                // filter the empty strings.\n                const targetMethodKeys = (await cacheAdapter.get(hitSourceKey)) || {};\n                addItem(targetMethodKeys, methodCacheKey);\n                await cacheAdapter.set(hitSourceKey, targetMethodKeys);\n            });\n            const saveRegexp = async () => {\n                // save the regexp source if regexp exists.\n                if (len(hitSourceRegexpSources)) {\n                    const regexpList = (await cacheAdapter.get(unifiedHitSourceRegexpCacheKey)) || [];\n                    // TODO: hitSourceRegexpSources needs to be deduplicated\n                    pushItem(regexpList, ...hitSourceRegexpSources);\n                    await cacheAdapter.set(unifiedHitSourceRegexpCacheKey, regexpList);\n                }\n            };\n            // parallel executing all async tasks.\n            await PromiseCls.all([...promises, saveRegexp()]);\n        }\n    }\n};\n/**\n * Delete stored response data\n * @param namespace namespace\n * @param key stored key\n * @param storage storage object\n */\nconst removeWithCacheAdapter = async (namespace, key, cacheAdapter) => {\n    const methodStoreKey = buildNamespacedCacheKey(namespace, key);\n    await cacheAdapter.remove(methodStoreKey);\n};\n/**\n * Get stored response data\n * @param namespace namespace\n * @param key stored key\n * @param storage storage object\n * @param tag Store tags. If the tag changes, the data will become invalid.\n */\nconst getRawWithCacheAdapter = async (namespace, key, cacheAdapter, tag) => {\n    const storagedData = await cacheAdapter.get(buildNamespacedCacheKey(namespace, key));\n    if (storagedData) {\n        // Eslint disable next line\n        const [dataUnused, expireTimestamp, storedTag] = storagedData;\n        // If there is no expiration time, it means that the data will never expire. Otherwise, you need to determine whether it has expired.\n        if (storedTag === tag && (!expireTimestamp || expireTimestamp > getTime())) {\n            return storagedData;\n        }\n        // If expired, delete cache\n        await removeWithCacheAdapter(namespace, key, cacheAdapter);\n    }\n};\n/**\n * Get stored response data\n * @param namespace namespace\n * @param key stored key\n * @param storage storage object\n * @param tag Store tags. If the tag changes, the data will become invalid.\n */\nconst getWithCacheAdapter = async (namespace, key, cacheAdapter, tag) => {\n    const rawData = await getRawWithCacheAdapter(namespace, key, cacheAdapter, tag);\n    return rawData ? rawData[0] : undefinedValue;\n};\n/**\n * clear all cached data\n */\nconst clearWithCacheAdapter = async (cacheAdapters) => PromiseCls.all(cacheAdapters.map(cacheAdapter => cacheAdapter.clear()));\n/**\n * query and delete target cache with key and name of source method instance.\n * @param sourceKey source method instance key\n * @param sourceName source method instance name\n * @param cacheAdapter cache adapter\n */\nconst hitTargetCacheWithCacheAdapter = async (sourceKey, sourceName, cacheAdapter) => {\n    const sourceNameStr = `${sourceName}`;\n    // map that recording the source key and target method keys.\n    const sourceTargetKeyMap = {};\n    // get hit key by method key.\n    const hitSourceKey = hitSourceStringCacheKey(sourceKey);\n    sourceTargetKeyMap[hitSourceKey] = await cacheAdapter.get(hitSourceKey);\n    let unifiedHitSourceRegexpChannel;\n    if (sourceName) {\n        const hitSourceName = hitSourceStringCacheKey(sourceNameStr);\n        // get hit key by method name if it is exists.\n        sourceTargetKeyMap[hitSourceName] = await cacheAdapter.get(hitSourceName);\n        // match regexped key by source method name and get hit key by method name.\n        unifiedHitSourceRegexpChannel = await cacheAdapter.get(unifiedHitSourceRegexpCacheKey);\n        const matchedRegexpStrings = [];\n        if (unifiedHitSourceRegexpChannel && len(unifiedHitSourceRegexpChannel)) {\n            forEach(unifiedHitSourceRegexpChannel, regexpStr => {\n                const [source, flag] = regexpStr.split(regexpSourceFlagSeparator);\n                if (newInstance(RegExpCls, source, flag).test(sourceNameStr)) {\n                    pushItem(matchedRegexpStrings, regexpStr);\n                }\n            });\n            // parallel get hit key by matched regexps.\n            await PromiseCls.all(mapItem(matchedRegexpStrings, async (regexpString) => {\n                const hitSourceRegexpString = hitSourceRegexpCacheKey(regexpString);\n                sourceTargetKeyMap[hitSourceRegexpString] = await cacheAdapter.get(hitSourceRegexpString);\n            }));\n        }\n    }\n    const removeWithTargetKey = async (targetKey) => {\n        try {\n            await cacheAdapter.remove(targetKey);\n            // loop sourceTargetKeyMap and remove this key to prevent unnecessary cost of IO.\n            for (const sourceKey in sourceTargetKeyMap) {\n                const targetKeys = sourceTargetKeyMap[sourceKey];\n                if (targetKeys) {\n                    deleteAttr(targetKeys, targetKey);\n                }\n            }\n        }\n        catch (_a) {\n            // the try-catch is used to prevent throwing error, cause throwing error in `Promise.all` below.\n        }\n    };\n    // now let's start to delete target caches.\n    // and filter the finished keys.\n    const accessedKeys = {};\n    await PromiseCls.all(mapItem(objectKeys(sourceTargetKeyMap), async (sourceKey) => {\n        const targetKeys = sourceTargetKeyMap[sourceKey];\n        if (targetKeys) {\n            const removingPromises = [];\n            for (const key in targetKeys) {\n                if (!accessedKeys[key]) {\n                    addItem(accessedKeys, key);\n                    pushItem(removingPromises, removeWithTargetKey(key));\n                }\n            }\n            await PromiseCls.all(removingPromises);\n        }\n    }));\n    // update source key if there is still has keys.\n    // remove source key if its keys is empty.\n    const unifiedHitSourceRegexpChannelLen = len(unifiedHitSourceRegexpChannel || []);\n    await PromiseCls.all(mapItem(objectKeys(sourceTargetKeyMap), async (sourceKey) => {\n        const targetKeys = sourceTargetKeyMap[sourceKey];\n        if (targetKeys) {\n            if (len(objectKeys(targetKeys))) {\n                await cacheAdapter.set(sourceKey, targetKeys);\n            }\n            else {\n                await cacheAdapter.remove(sourceKey);\n                // if this is a regexped key, need to remove it from unified regexp channel.\n                if (sourceKey.includes(hitSourceRegexpPrefix) && unifiedHitSourceRegexpChannel) {\n                    unifiedHitSourceRegexpChannel = filterItem(unifiedHitSourceRegexpChannel, rawRegexpStr => hitSourceRegexpCacheKey(rawRegexpStr) !== sourceKey);\n                }\n            }\n        }\n    }));\n    // update unified hit source regexp channel if its length was changed.\n    if (unifiedHitSourceRegexpChannelLen !== len(unifiedHitSourceRegexpChannel || [])) {\n        await cacheAdapter.set(unifiedHitSourceRegexpCacheKey, unifiedHitSourceRegexpChannel);\n    }\n};\n\nvar cloneMethod = (methodInstance) => {\n    const { data, config } = methodInstance;\n    const newConfig = { ...config };\n    const { headers = {}, params = {} } = newConfig;\n    const ctx = getContext(methodInstance);\n    newConfig.headers = { ...headers };\n    newConfig.params = isString(params) ? params : { ...params };\n    const newMethod = newInstance((Method), methodInstance.type, ctx, methodInstance.url, newConfig, data);\n    return objAssign(newMethod, {\n        ...methodInstance,\n        config: newConfig\n    });\n};\n\n/*\n * The matchers in the following three functions are Method instance matchers, which are divided into three situations:\n * 1. If the matcher is a Method instance, clear the cache of the Method instance.\n * 2. If matcher is a string or regular expression, clear the cache of all Method instances that meet the conditions.\n * 3. If no matcher is passed in, all caches will be cleared.\n */\n/**\n * Query cache\n * @param matcher Method instance matcher\n * @returns Cache data, return undefined if not found\n */\nconst queryCache = async (matcher, { policy = 'all' } = {}) => {\n    // if key exists, that means it's a method instance.\n    if (matcher && matcher.key) {\n        const { id, l1Cache, l2Cache } = getContext(matcher);\n        const methodKey = getMethodInternalKey(matcher);\n        const { f: cacheFor, c: controlled, s: store, e: expireMilliseconds, t: tag } = getLocalCacheConfigParam(matcher);\n        // if it's controlled cache, it will return the result of cacheFor function.\n        if (controlled) {\n            return cacheFor();\n        }\n        let cachedData = policy !== 'l2' ? await getWithCacheAdapter(id, methodKey, l1Cache) : undefinedValue;\n        if (policy === 'l2') {\n            cachedData = await getWithCacheAdapter(id, methodKey, l2Cache, tag);\n        }\n        else if (policy === 'all' && !cachedData) {\n            if (store && expireMilliseconds(STORAGE_RESTORE) > getTime()) {\n                cachedData = await getWithCacheAdapter(id, methodKey, l2Cache, tag);\n            }\n        }\n        return cachedData;\n    }\n};\n/**\n * Manually set cache response data. If the corresponding methodInstance sets persistent storage, the cache in the persistent storage will also be checked out.\n * @param matcher Method instance matcher cache data\n */\nconst setCache = async (matcher, dataOrUpdater, { policy = 'all' } = {}) => {\n    const methodInstances = isArray(matcher) ? matcher : [matcher];\n    const batchPromises = methodInstances.map(async (methodInstance) => {\n        const { hitSource } = methodInstance;\n        const { id, l1Cache, l2Cache } = getContext(methodInstance);\n        const methodKey = getMethodInternalKey(methodInstance);\n        const { e: expireMilliseconds, s: toStore, t: tag, c: controlled } = getLocalCacheConfigParam(methodInstance);\n        // don't set cache when it's controlled cache.\n        if (controlled) {\n            return;\n        }\n        let data = dataOrUpdater;\n        if (isFn(dataOrUpdater)) {\n            let cachedData = policy !== 'l2' ? await getWithCacheAdapter(id, methodKey, l1Cache) : undefinedValue;\n            if (policy === 'l2' ||\n                (policy === 'all' && !cachedData && toStore && expireMilliseconds(STORAGE_RESTORE) > getTime())) {\n                cachedData = await getWithCacheAdapter(id, methodKey, l2Cache, tag);\n            }\n            data = dataOrUpdater(cachedData);\n            if (data === undefinedValue) {\n                return;\n            }\n        }\n        return PromiseCls.all([\n            policy !== 'l2' && setWithCacheAdapter(id, methodKey, data, expireMilliseconds(MEMORY), l1Cache, hitSource),\n            policy === 'l2' || (policy === 'all' && toStore)\n                ? setWithCacheAdapter(id, methodKey, data, expireMilliseconds(STORAGE_RESTORE), l2Cache, hitSource, tag)\n                : undefinedValue\n        ]);\n    });\n    return PromiseCls.all(batchPromises);\n};\n/**\n * invalid cache\n * @param matcher Method instance matcher\n */\nconst invalidateCache = async (matcher) => {\n    if (!matcher) {\n        await PromiseCls.all([clearWithCacheAdapter(usingL1CacheAdapters), clearWithCacheAdapter(usingL2CacheAdapters)]);\n        return;\n    }\n    const methodInstances = isArray(matcher) ? matcher : [matcher];\n    const batchPromises = methodInstances.map(methodInstance => {\n        const { id, l1Cache, l2Cache } = getContext(methodInstance);\n        const { c: controlled, m: cacheMode } = getLocalCacheConfigParam(methodInstance);\n        // don't invalidate cache when it's controlled cache.\n        if (controlled) {\n            return;\n        }\n        const methodKey = getMethodInternalKey(methodInstance);\n        return PromiseCls.all([\n            removeWithCacheAdapter(id, methodKey, l1Cache),\n            cacheMode === STORAGE_RESTORE ? removeWithCacheAdapter(id, methodKey, l2Cache) : promiseResolve()\n        ]);\n    });\n    await PromiseCls.all(batchPromises);\n};\n/**\n * hit(invalidate) target caches by source method\n * this is the implementation of auto invalidate cache\n * @param sourceMethod source method instance\n */\nconst hitCacheBySource = async (sourceMethod) => {\n    // Find the hit target cache and invalidate its cache\n    // Control the automatic cache invalidation range through global configuration `autoHitCache`\n    const { autoHitCache } = globalConfigMap;\n    const { l1Cache, l2Cache } = getContext(sourceMethod);\n    const sourceKey = getMethodInternalKey(sourceMethod);\n    const { name: sourceName } = getConfig(sourceMethod);\n    const cacheAdaptersInvolved = {\n        global: [...usingL1CacheAdapters, ...usingL2CacheAdapters],\n        self: [l1Cache, l2Cache],\n        close: []\n    }[autoHitCache];\n    if (cacheAdaptersInvolved && len(cacheAdaptersInvolved)) {\n        await PromiseCls.all(mapItem(cacheAdaptersInvolved, involvedCacheAdapter => hitTargetCacheWithCacheAdapter(sourceKey, sourceName, involvedCacheAdapter)));\n    }\n};\n\nconst adapterReturnMap = {};\n/**\n * actual request function\n * @param method request method object\n * @param forceRequest Ignore cache\n * @returns response data\n */\nfunction sendRequest(methodInstance, forceRequest) {\n    let fromCache = trueValue;\n    let requestAdapterCtrlsPromiseResolveFn;\n    const requestAdapterCtrlsPromise = newInstance(PromiseCls, resolve => {\n        requestAdapterCtrlsPromiseResolveFn = resolve;\n    });\n    const response = async () => {\n        const { beforeRequest = noop, responded, requestAdapter, cacheLogger } = getOptions(methodInstance);\n        const methodKey = getMethodInternalKey(methodInstance);\n        const { s: toStorage, t: tag, m: cacheMode, e: expireMilliseconds } = getLocalCacheConfigParam(methodInstance);\n        const { id, l1Cache, l2Cache, snapshots } = getContext(methodInstance);\n        // Get controlled cache or uncontrolled cache\n        const { cacheFor } = getConfig(methodInstance);\n        const { hitSource: methodHitSource } = methodInstance;\n        // If the current method sets a controlled cache, check whether there is custom data\n        let cachedResponse = await (isFn(cacheFor)\n            ? cacheFor()\n            : // If it is a forced request, skip the step of getting it from the cache\n                // Otherwise, determine whether to use cached data\n                forceRequest\n                    ? undefinedValue\n                    : getWithCacheAdapter(id, methodKey, l1Cache));\n        // If it is storage restore mode and there is no data in the cache, the persistent data needs to be restored to the cache, and the cached expiration time must be used.\n        if (cacheMode === STORAGE_RESTORE && !cachedResponse && !forceRequest) {\n            const rawL2CacheData = await getRawWithCacheAdapter(id, methodKey, l2Cache, tag);\n            if (rawL2CacheData) {\n                const [l2Response, l2ExpireMilliseconds] = rawL2CacheData;\n                await setWithCacheAdapter(id, methodKey, l2Response, l2ExpireMilliseconds, l1Cache, methodHitSource);\n                cachedResponse = l2Response;\n            }\n        }\n        // Clone the method as a parameter and pass it to beforeRequest to prevent side effects when using the original method instance request multiple times.\n        // Place it after `let cachedResponse = await...` to solve the problem of first assigning promise to the method instance in method.send, otherwise the promise will be undefined in clonedMethod.\n        const clonedMethod = cloneMethod(methodInstance);\n        // Call the hook function before sending the request\n        // beforeRequest supports synchronous functions and asynchronous functions\n        await beforeRequest(clonedMethod);\n        const { baseURL, url: newUrl, type, data } = clonedMethod;\n        const { params = {}, headers = {}, transform = $self, shareRequest } = getConfig(clonedMethod);\n        const namespacedAdapterReturnMap = (adapterReturnMap[id] = adapterReturnMap[id] || {});\n        const requestBody = clonedMethod.data;\n        const requestBodyIsSpecial = isSpecialRequestBody(requestBody);\n        // Will not share the request when requestBody is special data\n        let requestAdapterCtrls = requestBodyIsSpecial ? undefinedValue : namespacedAdapterReturnMap[methodKey];\n        let responseSuccessHandler = $self;\n        let responseErrorHandler = undefinedValue;\n        let responseCompleteHandler = noop;\n        // uniform handler of onSuccess, onError, onComplete\n        if (isFn(responded)) {\n            responseSuccessHandler = responded;\n        }\n        else if (isPlainObject(responded)) {\n            const { onSuccess: successHandler, onError: errorHandler, onComplete: completeHandler } = responded;\n            responseSuccessHandler = isFn(successHandler) ? successHandler : responseSuccessHandler;\n            responseErrorHandler = isFn(errorHandler) ? errorHandler : responseErrorHandler;\n            responseCompleteHandler = isFn(completeHandler) ? completeHandler : responseCompleteHandler;\n        }\n        // If there is no cache, make a request\n        if (cachedResponse !== undefinedValue) {\n            requestAdapterCtrlsPromiseResolveFn(); // Ctrls will not be passed in when cache is encountered\n            // Print cache log\n            clonedMethod.fromCache = trueValue;\n            sloughFunction(cacheLogger, defaultCacheLogger)(cachedResponse, clonedMethod, cacheMode, tag);\n            responseCompleteHandler(clonedMethod);\n            return cachedResponse;\n        }\n        fromCache = falseValue;\n        if (!shareRequest || !requestAdapterCtrls) {\n            // Request data\n            const ctrls = requestAdapter({\n                url: buildCompletedURL(baseURL, newUrl, params),\n                type,\n                data,\n                headers\n            }, clonedMethod);\n            requestAdapterCtrls = namespacedAdapterReturnMap[methodKey] = ctrls;\n        }\n        // Pass request adapter ctrls to promise for use in on download, on upload and abort\n        requestAdapterCtrlsPromiseResolveFn(requestAdapterCtrls);\n        /**\n         * Process response tasks and do not cache data on failure\n         * @param responsePromise Respond to promise instances\n         * @param responseHeaders Request header\n         * @param callInSuccess Whether to call in success callback\n         * @returns Processed response\n         */\n        const handleResponseTask = async (handlerReturns, responseHeaders, callInSuccess = trueValue) => {\n            const responseData = await handlerReturns;\n            const transformedData = await transform(responseData, responseHeaders || {});\n            snapshots.save(methodInstance);\n            // Even if the cache operation fails, the response structure will be returned normally to avoid request errors caused by cache operation problems.\n            // The cache operation results can be obtained through `cacheAdapter.emitter.on('success' | 'fail', event => {})`\n            try {\n                // Automatic cache invalidation\n                await hitCacheBySource(clonedMethod);\n            }\n            catch (_a) { }\n            // Do not save cache when requestBody is special data\n            // Reason 1: Special data is generally submitted and requires interaction with the server.\n            // Reason 2: Special data is not convenient for generating cache keys\n            const toCache = !requestBody || !requestBodyIsSpecial;\n            // Use the latest expiration time after the response to cache data to avoid the problem of expiration time loss due to too long response time\n            if (toCache && callInSuccess) {\n                try {\n                    await PromiseCls.all([\n                        setWithCacheAdapter(id, methodKey, transformedData, expireMilliseconds(MEMORY), l1Cache, methodHitSource),\n                        toStorage &&\n                            setWithCacheAdapter(id, methodKey, transformedData, expireMilliseconds(STORAGE_RESTORE), l2Cache, methodHitSource, tag)\n                    ]);\n                }\n                catch (_b) { }\n            }\n            // Deep clone the transformed data before returning to avoid reference issues\n            // the `deepClone` will only clone array and plain object\n            return deepClone(transformedData);\n        };\n        return promiseFinally(promiseThen(PromiseCls.all([requestAdapterCtrls.response(), requestAdapterCtrls.headers()]), ([rawResponse, rawHeaders]) => {\n            // Regardless of whether the request succeeds or fails, the shared request needs to be removed first\n            deleteAttr(namespacedAdapterReturnMap, methodKey);\n            return handleResponseTask(responseSuccessHandler(rawResponse, clonedMethod), rawHeaders);\n        }, (error) => {\n            // Regardless of whether the request succeeds or fails, the shared request needs to be removed first\n            deleteAttr(namespacedAdapterReturnMap, methodKey);\n            return isFn(responseErrorHandler)\n                ? // When responding to an error, if no error is thrown, the successful response process will be processed, but the data will not be cached.\n                    handleResponseTask(responseErrorHandler(error, clonedMethod), undefinedValue, falseValue)\n                : promiseReject(error);\n        }), () => {\n            responseCompleteHandler(clonedMethod);\n        });\n    };\n    return {\n        // request interrupt function\n        abort: () => {\n            promiseThen(requestAdapterCtrlsPromise, requestAdapterCtrls => requestAdapterCtrls && requestAdapterCtrls.abort());\n        },\n        onDownload: (handler) => {\n            promiseThen(requestAdapterCtrlsPromise, requestAdapterCtrls => requestAdapterCtrls && requestAdapterCtrls.onDownload && requestAdapterCtrls.onDownload(handler));\n        },\n        onUpload: (handler) => {\n            promiseThen(requestAdapterCtrlsPromise, requestAdapterCtrls => requestAdapterCtrls && requestAdapterCtrls.onUpload && requestAdapterCtrls.onUpload(handler));\n        },\n        response,\n        fromCache: () => fromCache\n    };\n}\n\nconst offEventCallback = (offHandler, handlers) => () => {\n    const index = handlers.indexOf(offHandler);\n    index >= 0 && handlers.splice(index, 1);\n};\nclass Method {\n    constructor(type, context, url, config, data) {\n        this.dhs = [];\n        this.uhs = [];\n        this.fromCache = undefinedValue;\n        const abortRequest = () => {\n            abortRequest.a();\n        };\n        abortRequest.a = noop;\n        type = type.toUpperCase();\n        const instance = this;\n        const contextOptions = getContextOptions(context);\n        instance.abort = abortRequest;\n        instance.baseURL = contextOptions.baseURL || '';\n        instance.url = url;\n        instance.type = type;\n        instance.context = context;\n        // Merge request-related global configuration into the method object\n        const contextConcatConfig = {};\n        const mergedLocalCacheKey = 'cacheFor';\n        const globalLocalCache = isPlainObject(contextOptions[mergedLocalCacheKey])\n            ? contextOptions[mergedLocalCacheKey][type]\n            : undefinedValue;\n        const hitSource = config && config.hitSource;\n        // Merge parameters\n        forEach(['timeout', 'shareRequest'], mergedKey => {\n            if (contextOptions[mergedKey] !== undefinedValue) {\n                contextConcatConfig[mergedKey] = contextOptions[mergedKey];\n            }\n        });\n        // Merge local cache\n        if (globalLocalCache !== undefinedValue) {\n            contextConcatConfig[mergedLocalCacheKey] = globalLocalCache;\n        }\n        // Unify hit sources into arrays and convert them into method keys when there are method instances\n        if (hitSource) {\n            instance.hitSource = mapItem(isArray(hitSource) ? hitSource : [hitSource], sourceItem => instanceOf(sourceItem, Method) ? getMethodInternalKey(sourceItem) : sourceItem);\n            deleteAttr(config, 'hitSource');\n        }\n        instance.config = {\n            ...contextConcatConfig,\n            headers: {},\n            params: {},\n            ...(config || {})\n        };\n        instance.data = data;\n        instance.meta = config ? config.meta : instance.meta;\n        // The original key needs to be used externally instead of generating the key in real time.\n        // The reason is that the parameters of the method may pass in reference type values, but when the reference type value changes externally, the key generated in real time also changes, so it is more accurate to use the initial key.\n        instance.key = instance.generateKey();\n    }\n    /**\n     * Bind download progress callback function\n     * @param progressHandler Download progress callback function\n     * @version 2.17.0\n     * @return unbind function\n     */\n    onDownload(downloadHandler) {\n        pushItem(this.dhs, downloadHandler);\n        return offEventCallback(downloadHandler, this.dhs);\n    }\n    /**\n     * Bind upload progress callback function\n     * @param progressHandler Upload progress callback function\n     * @version 2.17.0\n     * @return unbind function\n     */\n    onUpload(uploadHandler) {\n        pushItem(this.uhs, uploadHandler);\n        return offEventCallback(uploadHandler, this.uhs);\n    }\n    /**\n     * Send a request through a method instance and return a promise object\n     */\n    send(forceRequest = falseValue) {\n        const instance = this;\n        const { response, onDownload, onUpload, abort, fromCache } = sendRequest(instance, forceRequest);\n        len(instance.dhs) > 0 &&\n            onDownload((loaded, total) => forEach(instance.dhs, handler => handler({ loaded, total })));\n        len(instance.uhs) > 0 && onUpload((loaded, total) => forEach(instance.uhs, handler => handler({ loaded, total })));\n        // The interrupt function is bound to the method instance for each request. The user can also interrupt the current request through method instance.abort()\n        instance.abort.a = abort;\n        instance.fromCache = undefinedValue;\n        instance.promise = promiseThen(response(), r => {\n            instance.fromCache = fromCache();\n            return r;\n        });\n        return instance.promise;\n    }\n    /**\n     * Set the method name, if there is already a name it will be overwritten\n     * @param name method name\n     */\n    setName(name) {\n        getConfig(this).name = name;\n    }\n    generateKey() {\n        return key(this);\n    }\n    /**\n     * Bind callbacks for resolve and/or reject Promise\n     * @param onfulfilled The callback to be executed when resolving the Promise\n     * @param onrejected The callback to be executed when the Promise is rejected\n     * @returns Returns a Promise for executing any callbacks\n     */\n    then(onfulfilled, onrejected) {\n        return promiseThen(this.send(), onfulfilled, onrejected);\n    }\n    /**\n     * Bind a callback only for reject Promise\n     * @param onrejected The callback to be executed when the Promise is rejected\n     * @returns Returns a Promise that completes the callback\n     */\n    catch(onrejected) {\n        return promiseCatch(this.send(), onrejected);\n    }\n    /**\n     * Bind a callback that is called when the Promise is resolved (resolve or reject)\n     * @param onfinally Callback executed when Promise is resolved (resolve or reject).\n     * @return Returns a Promise that completes the callback.\n     */\n    finally(onfinally) {\n        return promiseFinally(this.send(), onfinally);\n    }\n}\n\n/**\n * Custom assertion function, throws an error when the expression is false\n * @param expression Judgment expression, true or false\n * @param msg assert message\n */\nconst myAssert = createAssert();\n\n// local storage will not fail the operation.\nconst EVENT_SUCCESS_KEY = 'success';\nconst memoryAdapter = () => {\n    let l1Cache = {};\n    const l1CacheEmitter = createEventManager();\n    const adapter = {\n        set(key, value) {\n            l1Cache[key] = value;\n            l1CacheEmitter.emit(EVENT_SUCCESS_KEY, { type: 'set', key, value, container: l1Cache });\n        },\n        get: key => {\n            const value = l1Cache[key];\n            l1CacheEmitter.emit(EVENT_SUCCESS_KEY, { type: 'get', key, value, container: l1Cache });\n            return value;\n        },\n        remove(key) {\n            deleteAttr(l1Cache, key);\n            l1CacheEmitter.emit(EVENT_SUCCESS_KEY, { type: 'remove', key, container: l1Cache });\n        },\n        clear: () => {\n            l1Cache = {};\n            l1CacheEmitter.emit(EVENT_SUCCESS_KEY, { type: 'clear', key: '', container: l1Cache });\n        },\n        emitter: l1CacheEmitter\n    };\n    return adapter;\n};\nconst localStorageAdapter = () => {\n    const l2CacheEmitter = createEventManager();\n    const instance = localStorage;\n    const adapter = {\n        set: (key, value) => {\n            instance.setItem(key, JSONStringify(value));\n            l2CacheEmitter.emit(EVENT_SUCCESS_KEY, { type: 'set', key, value, container: instance });\n        },\n        get: key => {\n            const data = instance.getItem(key);\n            const value = data ? JSONParse(data) : data;\n            l2CacheEmitter.emit(EVENT_SUCCESS_KEY, { type: 'get', key, value, container: instance });\n            return value;\n        },\n        remove: key => {\n            instance.removeItem(key);\n            l2CacheEmitter.emit(EVENT_SUCCESS_KEY, { type: 'remove', key, container: instance });\n        },\n        clear: () => {\n            instance.clear();\n            l2CacheEmitter.emit(EVENT_SUCCESS_KEY, { type: 'clear', key: '', container: instance });\n        },\n        emitter: l2CacheEmitter\n    };\n    return adapter;\n};\nconst placeholderAdapter = () => {\n    const l2CacheNotDefinedAssert = () => {\n        myAssert(falseValue, 'l2Cache is not defined.');\n    };\n    return {\n        set: () => {\n            l2CacheNotDefinedAssert();\n        },\n        get: () => {\n            l2CacheNotDefinedAssert();\n            return undefinedValue;\n        },\n        remove: () => {\n            l2CacheNotDefinedAssert();\n        },\n        clear: () => { }\n    };\n};\n\nconst SetCls = Set;\nclass MethodSnapshotContainer {\n    constructor(capacity) {\n        /**\n         * Method instance snapshot collection, method instances that have sent requests will be saved\n         */\n        this.records = {};\n        this.occupy = 0;\n        myAssert(capacity >= 0, 'expected snapshots limit to be >= 0');\n        this.capacity = capacity;\n    }\n    /**\n     * Save method instance snapshot\n     * @param methodInstance method instance\n     */\n    save(methodInstance) {\n        const { name } = getConfig(methodInstance);\n        const { records, occupy, capacity } = this;\n        if (name && occupy < capacity) {\n            // Using the name of the method as the key, save the method instance to the snapshot\n            const targetSnapshots = (records[name] = records[name] || newInstance(SetCls));\n            targetSnapshots.add(methodInstance);\n            // Statistical quantity\n            this.occupy += 1;\n        }\n    }\n    /**\n     * Get a Method instance snapshot, which will filter out the corresponding Method instance based on the matcher\n     * @param matcher Matching snapshot name, which can be a string or regular expression, or an object with a filter function\n     * @returns Array of matched Method instance snapshots\n     */\n    match(matcher, matchAll = true) {\n        // Unify the filter parameters into name matcher and match handler\n        let nameString;\n        let nameReg;\n        let matchHandler;\n        let nameMatcher = matcher;\n        if (isPlainObject(matcher)) {\n            nameMatcher = matcher.name;\n            matchHandler = matcher.filter;\n        }\n        if (instanceOf(nameMatcher, RegExpCls)) {\n            nameReg = nameMatcher;\n        }\n        else if (isString(nameMatcher)) {\n            nameString = nameMatcher;\n        }\n        const { records } = this;\n        // Get the corresponding method instance snapshot through the deconstructed name matcher and filter handler\n        let matches = newInstance((SetCls));\n        // If the namespace parameter is provided, it will only be searched in this namespace, otherwise it will be searched in all cached data.\n        if (nameString) {\n            matches = records[nameString] || matches;\n        }\n        else if (nameReg) {\n            forEach(filterItem(objectKeys(records), methodName => nameReg.test(methodName)), methodName => {\n                records[methodName].forEach(method => matches.add(method));\n            });\n        }\n        const fromMatchesArray = isFn(matchHandler) ? filterItem([...matches], matchHandler) : [...matches];\n        return (matchAll ? fromMatchesArray : fromMatchesArray[0]);\n    }\n}\n\nconst typeGet = 'GET';\nconst typeHead = 'HEAD';\nconst typePost = 'POST';\nconst typePut = 'PUT';\nconst typePatch = 'PATCH';\nconst typeDelete = 'DELETE';\nconst typeOptions = 'OPTIONS';\nconst defaultAlovaOptions = {\n    /**\n     * GET requests are cached for 5 minutes (300000 milliseconds) by default, and other requests are not cached by default.\n     */\n    cacheFor: {\n        [typeGet]: 300000\n    },\n    /**\n     * Share requests default to true\n     */\n    shareRequest: trueValue,\n    /**\n     * Number of method snapshots, default is 1000\n     */\n    snapshots: 1000\n};\nlet idCount = 0;\nclass Alova {\n    constructor(options) {\n        var _a, _b;\n        const instance = this;\n        instance.id = (options.id || (idCount += 1)).toString();\n        // If storage is not specified, local storage is used by default.\n        instance.l1Cache = options.l1Cache || memoryAdapter();\n        instance.l2Cache =\n            options.l2Cache || (typeof localStorage !== 'undefined' ? localStorageAdapter() : placeholderAdapter());\n        // Merge default options\n        instance.options = {\n            ...defaultAlovaOptions,\n            ...options\n        };\n        instance.snapshots = newInstance((MethodSnapshotContainer), (_b = (_a = options.snapshots) !== null && _a !== void 0 ? _a : defaultAlovaOptions.snapshots) !== null && _b !== void 0 ? _b : 0);\n    }\n    Request(config) {\n        return newInstance((Method), config.method || typeGet, this, config.url, config, config.data);\n    }\n    Get(url, config) {\n        return newInstance((Method), typeGet, this, url, config);\n    }\n    Post(url, data, config) {\n        return newInstance((Method), typePost, this, url, config, data);\n    }\n    Delete(url, data, config) {\n        return newInstance((Method), typeDelete, this, url, config, data);\n    }\n    Put(url, data, config) {\n        return newInstance((Method), typePut, this, url, config, data);\n    }\n    Head(url, config) {\n        return newInstance((Method), typeHead, this, url, config);\n    }\n    Patch(url, data, config) {\n        return newInstance((Method), typePatch, this, url, config, data);\n    }\n    Options(url, config) {\n        return newInstance((Method), typeOptions, this, url, config);\n    }\n}\nlet boundStatesHook = undefinedValue;\nconst usingL1CacheAdapters = [];\nconst usingL2CacheAdapters = [];\n/**\n * create an alova instance.\n * @param options alova configuration.\n * @returns alova instance.\n */\nconst createAlova = (options) => {\n    const alovaInstance = newInstance((Alova), options);\n    const newStatesHook = alovaInstance.options.statesHook;\n    if (boundStatesHook && newStatesHook) {\n        myAssert(boundStatesHook.name === newStatesHook.name, 'expected to use the same `statesHook`');\n    }\n    boundStatesHook = newStatesHook;\n    const { l1Cache, l2Cache } = alovaInstance;\n    !usingL1CacheAdapters.includes(l1Cache) && pushItem(usingL1CacheAdapters, l1Cache);\n    !usingL2CacheAdapters.includes(l2Cache) && pushItem(usingL2CacheAdapters, l2Cache);\n    return alovaInstance;\n};\n\nconst promiseStatesHook = () => {\n    myAssert(boundStatesHook, '`statesHook` is not set in alova instance');\n    return boundStatesHook;\n};\n\nexport { Method, createAlova, globalConfig, globalConfigMap, hitCacheBySource, invalidateCache, promiseStatesHook, queryCache, setCache };\n"], "mappings": ";;;AAOA,IAAM,WAAW;AAEjB,IAAM,aAAa;AACnB,IAAM,iBAAiB,CAAC,UAAU,WAAW,QAAQ,KAAK;AAC1D,IAAM,gBAAgB,CAAC,UAAU,WAAW,OAAO,KAAK;AACxD,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,iBAAiB;AACvB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,cAAc,CAAC,SAAS,aAAa,eAAe,QAAQ,KAAK,aAAa,UAAU;AAC9F,IAAM,eAAe,CAAC,SAAS,eAAe,QAAQ,MAAM,UAAU;AACtE,IAAM,iBAAiB,CAAC,SAAS,cAAc,QAAQ,QAAQ,SAAS;AAExE,IAAM,gBAAgB,CAAC,OAAO,UAAU,UAAU,KAAK,UAAU,OAAO,UAAU,KAAK;AACvF,IAAM,YAAY,CAAC,UAAU,KAAK,MAAM,KAAK;AAG7C,IAAM,aAAa,CAAC,QAAQ,UAAU,KAAK,GAAG;AAE9C,IAAM,UAAU,CAAC,KAAK,OAAO,IAAI,QAAQ,EAAE;AAC3C,IAAM,WAAW,CAAC,QAAQ,SAAS,IAAI,KAAK,GAAG,IAAI;AACnD,IAAM,UAAU,CAAC,KAAK,eAAe,IAAI,IAAI,UAAU;AACvD,IAAM,aAAa,CAAC,KAAK,cAAc,IAAI,OAAO,SAAS;AAI3D,IAAM,MAAM,CAAC,SAAS,KAAK;AAC3B,IAAM,UAAU,CAAC,QAAQ,MAAM,QAAQ,GAAG;AAC1C,IAAM,aAAa,CAAC,KAAK,SAAS,OAAO,IAAI,IAAI;AACjD,IAAM,SAAS,CAAC,QAAQ,OAAO;AAO/B,IAAM,QAAQ,OAAO,WAAW,aAAa,OAAO,YAAY,WAAW,CAAC,QAAQ,UAAU,OAAO,SAAS;AAG9G,IAAM,SAAS;AAEf,IAAM,kBAAkB;AAKxB,IAAM,OAAO,MAAM;AAAE;AAOrB,IAAM,QAAQ,CAAC,QAAQ;AAKvB,IAAM,OAAO,CAAC,QAAQ,OAAO,GAAG,MAAM;AAKtC,IAAM,WAAW,CAAC,QAAQ,OAAO,GAAG,MAAM,YAAY,CAAC,OAAO,MAAM,GAAG;AAKvE,IAAM,WAAW,CAAC,QAAQ,OAAO,GAAG,MAAM;AAS1C,IAAM,iBAAiB,CAAC,QAAQ,UAAU,UAAU,SAAS,KAAK,GAAG;AAKrE,IAAM,gBAAgB,CAAC,QAAQ,eAAe,GAAG,MAAM;AAKvD,IAAM,aAAa,CAAC,KAAK,QAAQ,eAAe;AAKhD,IAAM,UAAU,CAAC,SAAU,OAAO,KAAK,QAAQ,IAAI,KAAK,IAAI;AAI5D,IAAM,aAAa,CAAC,mBAAmB,eAAe;AAKtD,IAAM,YAAY,CAAC,mBAAmB,eAAe;AAIrD,IAAM,oBAAoB,CAAC,kBAAkB,cAAc;AAI3D,IAAM,aAAa,CAAC,mBAAmB,kBAAkB,WAAW,cAAc,CAAC;AAKnF,IAAM,MAAM,CAAC,mBAAmB;AAC5B,QAAM,EAAE,QAAQ,QAAQ,IAAI,UAAU,cAAc;AACpD,SAAO,cAAc,CAAC,eAAe,MAAM,eAAe,KAAK,QAAQ,eAAe,MAAM,OAAO,CAAC;AACxG;AAYA,IAAM,uBAAuB,CAAC,mBAAmB,eAAe;AAgBhE,IAAM,uBAAuB,CAAC,SAAS;AACnC,QAAM,iBAAiB,eAAe,IAAI;AAC1C,SAAQ,+DAA+D,KAAK,cAAc,KAAK,WAAW,MAAM,WAAW;AAC/H;AACA,IAAM,YAAY,CAAC,WAAW,YAAY,UAAU,OAAO,QAAQ,GAAG,OAAO;AAqC7E,IAAM,2BAA2B,CAAC,mBAAmB;AACjD,QAAM,EAAE,SAAS,IAAI,UAAU,cAAc;AAC7C,QAAM,mBAAmB,CAAC,gBAAgB,SAAS,WAAW,IAAI,QAAQ,IAAI,cAAc,QAAQ,eAAe,cAAc;AACjI,MAAI,YAAY;AAChB,MAAI,SAAS,MAAM;AACnB,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,QAAM,aAAa,KAAK,QAAQ;AAChC,MAAI,CAAC,YAAY;AACb,QAAI,eAAe;AACnB,QAAI,cAAc,QAAQ,GAAG;AACzB,YAAM,EAAE,OAAO,QAAQ,QAAAA,SAAQ,KAAK,UAAU,IAAI,YAAY,CAAC;AAC/D,kBAAY;AACZ,cAAQ,SAAS;AACjB,YAAM,YAAY,UAAU,SAAS,IAAI;AACzC,qBAAeA;AAAA,IACnB;AACA,aAAS,CAAC,SAAS,iBAAiB,KAAK,YAAY,IAAI,aAAa,EAAE,QAAQ,gBAAgB,KAAK,CAAC,IAAI,YAAY;AAAA,EAC1H;AACA,SAAO;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACP;AACJ;AAMA,IAAM,cAAc,CAAC,QAAQ,SAAS,IAAI,IAAI,GAAG,IAAI;AAOrD,IAAM,iBAAiB,CAAC,KAAK,cAAc,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,YAAY,SAAS,EAAE,SAAS,GAAG,IAAI,YAAY;AA8ElH,IAAM,iBAAiB;AAIvB,IAAM,0BAA0B,CAAC,WAAWC,SAAQ,iBAAiB,YAAYA;AAuBjF,IAAM,oBAAoB,CAAC,SAAS,KAAK,WAAW;AAEhD,QAAM,mBAAmB,gBAAgB,KAAK,GAAG;AACjD,MAAI,CAAC,kBAAkB;AAEnB,cAAU,QAAQ,SAAS,GAAG,IAAI,QAAQ,MAAM,GAAG,EAAE,IAAI;AAGzD,QAAI,QAAQ,IAAI;AAGZ,YAAM,IAAI,WAAW,GAAG,IAAI,MAAM,IAAI,GAAG;AAAA,IAC7C;AAAA,EACJ;AAEA,QAAM,cAAc,mBAAmB,MAAM,UAAU;AAGvD,QAAM,YAAY,SAAS,MAAM,IAC3B,SACA,QAAQ,WAAW,WAAW,MAAM,GAAG,CAAAC,SAAO,OAAOA,IAAG,MAAM,cAAc,GAAG,CAAAA,SAAO,GAAGA,IAAG,IAAI,OAAOA,IAAG,CAAC,EAAE,EAAE,KAAK,GAAG;AAE7H,SAAO,YACD,CAAC,YAAY,SAAS,GAAG,IACrB,GAAG,WAAW,IAAI,SAAS,KAC3B,GAAG,WAAW,IAAI,SAAS,KAC/B;AACV;AAOA,IAAM,YAAY,CAAC,QAAQ;AACvB,MAAI,QAAQ,GAAG,GAAG;AACd,WAAO,QAAQ,KAAK,SAAS;AAAA,EACjC;AACA,MAAI,cAAc,GAAG,KAAK,IAAI,gBAAgB,WAAW;AACrD,UAAM,QAAQ,CAAC;AACf,YAAQ,WAAW,GAAG,GAAG,CAAAA,SAAO;AAC5B,YAAMA,IAAG,IAAI,UAAU,IAAIA,IAAG,CAAC;AAAA,IACnC,CAAC;AACD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAKA,IAAM,aAAN,cAAyB,MAAM;AAAA,EAC3B,YAAY,QAAQ,SAAS,WAAW;AACpC,UAAM,WAAW,YAAY;AAAA;AAAA,2CAAgD,SAAS,KAAK,GAAG;AAC9F,SAAK,OAAO,SAAS,SAAS,IAAI,MAAM,KAAK,EAAE;AAAA,EACnD;AACJ;AAOA,IAAM,eAAe,CAAC,SAAS,OAAO,CAAC,YAAY,SAAS,cAAc;AACtE,MAAI,CAAC,YAAY;AACb,UAAM,YAAY,YAAY,QAAQ,SAAS,SAAS;AAAA,EAC5D;AACJ;AAYA,IAAM,qBAAqB,MAAM;AAC7B,QAAM,WAAW,CAAC;AAClB,SAAO;AAAA,IACH;AAAA,IACA,GAAG,MAAM,SAAS;AACd,YAAM,gBAAiB,SAAS,IAAI,IAAI,SAAS,IAAI,KAAK,CAAC;AAC3D,eAAS,eAAe,OAAO;AAE/B,aAAO,MAAM;AACT,iBAAS,IAAI,IAAI,WAAW,eAAe,UAAQ,SAAS,OAAO;AAAA,MACvE;AAAA,IACJ;AAAA,IACA,IAAI,MAAM,SAAS;AACf,YAAM,WAAW,SAAS,IAAI;AAC9B,UAAI,CAAC,UAAU;AACX;AAAA,MACJ;AACA,UAAI,SAAS;AACT,cAAM,QAAQ,SAAS,QAAQ,OAAO;AACtC,gBAAQ,MAAM,SAAS,OAAO,OAAO,CAAC;AAAA,MAC1C,OACK;AACD,eAAO,SAAS,IAAI;AAAA,MACxB;AAAA,IACJ;AAAA,IACA,KAAK,MAAM,OAAO;AACd,YAAM,WAAW,SAAS,IAAI,KAAK,CAAC;AACpC,aAAO,QAAQ,UAAU,aAAW,QAAQ,KAAK,CAAC;AAAA,IACtD;AAAA,EACJ;AACJ;;;ACxbA,IAAI,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,KAAK;AACT;AAKA,IAAI,eAAe,CAAC,WAAW;AAC3B,oBAAkB;AAAA,IACd,GAAG;AAAA,IACH,GAAG;AAAA,EACP;AACJ;AAEA,IAAM,aAAa;AAInB,IAAI,qBAAqB,CAAC,UAAU,gBAAgB,WAAW,QAAQ;AACnE,QAAM,OAAO;AAEb,QAAM,MAAM,IAAI,SAAS,QAAQ,IAAI,GAAG,IAAI;AAC5C,QAAM,EAAE,IAAI,IAAI;AAChB,QAAM,gBAAgB,cAAc;AACpC,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,WAAW,cAAc,GAAG;AAClC,QAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI,CAAC,EAAE,KAAK,GAAG;AACxD,MAAI,gBAAgB,KAAK;AACrB,QAAI,SAAS,QAAQ;AACrB,QAAI,YAAY,WAAW,QAAQ;AACnC,QAAI,YAAY,WAAW,SAAS;AACpC,qBAAiB,IAAI,YAAY,WAAW,GAAG;AAC/C,QAAI,YAAY,SAAS,CAAC;AAAA,EAC9B,OACK;AACD,SAAK,iBACC,KAAK,eAAe,cAAc,0DAA0D,GAAG,IAC/F,IAAI,SAAS,QAAQ;AAC3B,QAAI,aAAa,YAAY,QAAQ;AACrC,QAAI,YAAY,YAAY,SAAS;AACrC,qBAAiB,IAAI,WAAW,YAAY,GAAG;AAC/C,QAAI,cAAc,YAAY,cAAc;AAC5C,SAAK,WAAW,KAAK,SAAS,IAAI,IAAI,YAAY,SAAS,CAAC;AAAA,EAChE;AACJ;AAEA,IAAM,0BAA0B,CAACC,SAAQ,OAAOA,IAAG;AACnD,IAAM,wBAAwB;AAC9B,IAAM,0BAA0B,CAAC,cAAc,wBAAwB;AACvE,IAAM,iCAAiC;AACvC,IAAM,4BAA4B;AAClC,IAAM,UAAU,CAAC,KAAK,SAAS;AAC3B,MAAI,IAAI,IAAI;AAChB;AAUA,IAAM,sBAAsB,OAAO,WAAWA,MAAK,MAAM,iBAAiB,cAAc,WAAW,QAAQ;AAEvG,MAAI,kBAAkB,QAAQ,KAAK,MAAM;AACrC,UAAM,iBAAiB,wBAAwB,WAAWA,IAAG;AAC7D,UAAM,aAAa,IAAI,gBAAgB,WAAW,CAAC,MAAM,oBAAoB,WAAW,iBAAiB,iBAAiB,GAAG,GAAG,OAAO,CAAC;AA0BxI,QAAI,WAAW;AAEX,YAAM,gBAAgB,CAAC;AACvB,YAAM,yBAAyB,CAAC;AAChC,cAAQ,WAAW,gBAAc;AAC7B,cAAM,WAAW,WAAW,YAAY,SAAS;AACjD,cAAM,qBAAqB,WACrB,WAAW,UAAU,WAAW,QAAQ,4BAA4B,WAAW,QAAQ,MACvF;AACN,YAAI,oBAAoB;AACpB,cAAI,YAAY,CAAC,cAAc,kBAAkB,GAAG;AAChD,qBAAS,wBAAwB,kBAAkB;AAAA,UACvD;AACA,kBAAQ,eAAe,WAAW,wBAAwB,kBAAkB,IAAI,wBAAwB,kBAAkB,CAAC;AAAA,QAC/H;AAAA,MACJ,CAAC;AAED,YAAM,WAAW,QAAQ,WAAW,aAAa,GAAG,OAAO,iBAAiB;AAExE,cAAM,mBAAoB,MAAM,aAAa,IAAI,YAAY,KAAM,CAAC;AACpE,gBAAQ,kBAAkB,cAAc;AACxC,cAAM,aAAa,IAAI,cAAc,gBAAgB;AAAA,MACzD,CAAC;AACD,YAAM,aAAa,YAAY;AAE3B,YAAI,IAAI,sBAAsB,GAAG;AAC7B,gBAAM,aAAc,MAAM,aAAa,IAAI,8BAA8B,KAAM,CAAC;AAEhF,mBAAS,YAAY,GAAG,sBAAsB;AAC9C,gBAAM,aAAa,IAAI,gCAAgC,UAAU;AAAA,QACrE;AAAA,MACJ;AAEA,YAAM,WAAW,IAAI,CAAC,GAAG,UAAU,WAAW,CAAC,CAAC;AAAA,IACpD;AAAA,EACJ;AACJ;AAOA,IAAM,yBAAyB,OAAO,WAAWA,MAAK,iBAAiB;AACnE,QAAM,iBAAiB,wBAAwB,WAAWA,IAAG;AAC7D,QAAM,aAAa,OAAO,cAAc;AAC5C;AAQA,IAAM,yBAAyB,OAAO,WAAWA,MAAK,cAAc,QAAQ;AACxE,QAAM,eAAe,MAAM,aAAa,IAAI,wBAAwB,WAAWA,IAAG,CAAC;AACnF,MAAI,cAAc;AAEd,UAAM,CAAC,YAAY,iBAAiB,SAAS,IAAI;AAEjD,QAAI,cAAc,QAAQ,CAAC,mBAAmB,kBAAkB,QAAQ,IAAI;AACxE,aAAO;AAAA,IACX;AAEA,UAAM,uBAAuB,WAAWA,MAAK,YAAY;AAAA,EAC7D;AACJ;AAQA,IAAM,sBAAsB,OAAO,WAAWA,MAAK,cAAc,QAAQ;AACrE,QAAM,UAAU,MAAM,uBAAuB,WAAWA,MAAK,cAAc,GAAG;AAC9E,SAAO,UAAU,QAAQ,CAAC,IAAI;AAClC;AAIA,IAAM,wBAAwB,OAAO,kBAAkB,WAAW,IAAI,cAAc,IAAI,kBAAgB,aAAa,MAAM,CAAC,CAAC;AAO7H,IAAM,iCAAiC,OAAO,WAAW,YAAY,iBAAiB;AAClF,QAAM,gBAAgB,GAAG,UAAU;AAEnC,QAAM,qBAAqB,CAAC;AAE5B,QAAM,eAAe,wBAAwB,SAAS;AACtD,qBAAmB,YAAY,IAAI,MAAM,aAAa,IAAI,YAAY;AACtE,MAAI;AACJ,MAAI,YAAY;AACZ,UAAM,gBAAgB,wBAAwB,aAAa;AAE3D,uBAAmB,aAAa,IAAI,MAAM,aAAa,IAAI,aAAa;AAExE,oCAAgC,MAAM,aAAa,IAAI,8BAA8B;AACrF,UAAM,uBAAuB,CAAC;AAC9B,QAAI,iCAAiC,IAAI,6BAA6B,GAAG;AACrE,cAAQ,+BAA+B,eAAa;AAChD,cAAM,CAAC,QAAQ,IAAI,IAAI,UAAU,MAAM,yBAAyB;AAChE,YAAI,YAAY,WAAW,QAAQ,IAAI,EAAE,KAAK,aAAa,GAAG;AAC1D,mBAAS,sBAAsB,SAAS;AAAA,QAC5C;AAAA,MACJ,CAAC;AAED,YAAM,WAAW,IAAI,QAAQ,sBAAsB,OAAO,iBAAiB;AACvE,cAAM,wBAAwB,wBAAwB,YAAY;AAClE,2BAAmB,qBAAqB,IAAI,MAAM,aAAa,IAAI,qBAAqB;AAAA,MAC5F,CAAC,CAAC;AAAA,IACN;AAAA,EACJ;AACA,QAAM,sBAAsB,OAAO,cAAc;AAC7C,QAAI;AACA,YAAM,aAAa,OAAO,SAAS;AAEnC,iBAAWC,cAAa,oBAAoB;AACxC,cAAM,aAAa,mBAAmBA,UAAS;AAC/C,YAAI,YAAY;AACZ,qBAAW,YAAY,SAAS;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ,SACO,IAAI;AAAA,IAEX;AAAA,EACJ;AAGA,QAAM,eAAe,CAAC;AACtB,QAAM,WAAW,IAAI,QAAQ,WAAW,kBAAkB,GAAG,OAAOA,eAAc;AAC9E,UAAM,aAAa,mBAAmBA,UAAS;AAC/C,QAAI,YAAY;AACZ,YAAM,mBAAmB,CAAC;AAC1B,iBAAWD,QAAO,YAAY;AAC1B,YAAI,CAAC,aAAaA,IAAG,GAAG;AACpB,kBAAQ,cAAcA,IAAG;AACzB,mBAAS,kBAAkB,oBAAoBA,IAAG,CAAC;AAAA,QACvD;AAAA,MACJ;AACA,YAAM,WAAW,IAAI,gBAAgB;AAAA,IACzC;AAAA,EACJ,CAAC,CAAC;AAGF,QAAM,mCAAmC,IAAI,iCAAiC,CAAC,CAAC;AAChF,QAAM,WAAW,IAAI,QAAQ,WAAW,kBAAkB,GAAG,OAAOC,eAAc;AAC9E,UAAM,aAAa,mBAAmBA,UAAS;AAC/C,QAAI,YAAY;AACZ,UAAI,IAAI,WAAW,UAAU,CAAC,GAAG;AAC7B,cAAM,aAAa,IAAIA,YAAW,UAAU;AAAA,MAChD,OACK;AACD,cAAM,aAAa,OAAOA,UAAS;AAEnC,YAAIA,WAAU,SAAS,qBAAqB,KAAK,+BAA+B;AAC5E,0CAAgC,WAAW,+BAA+B,kBAAgB,wBAAwB,YAAY,MAAMA,UAAS;AAAA,QACjJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC,CAAC;AAEF,MAAI,qCAAqC,IAAI,iCAAiC,CAAC,CAAC,GAAG;AAC/E,UAAM,aAAa,IAAI,gCAAgC,6BAA6B;AAAA,EACxF;AACJ;AAEA,IAAI,cAAc,CAAC,mBAAmB;AAClC,QAAM,EAAE,MAAM,OAAO,IAAI;AACzB,QAAM,YAAY,EAAE,GAAG,OAAO;AAC9B,QAAM,EAAE,UAAU,CAAC,GAAG,SAAS,CAAC,EAAE,IAAI;AACtC,QAAM,MAAM,WAAW,cAAc;AACrC,YAAU,UAAU,EAAE,GAAG,QAAQ;AACjC,YAAU,SAAS,SAAS,MAAM,IAAI,SAAS,EAAE,GAAG,OAAO;AAC3D,QAAM,YAAY,YAAa,QAAS,eAAe,MAAM,KAAK,eAAe,KAAK,WAAW,IAAI;AACrG,SAAO,UAAU,WAAW;AAAA,IACxB,GAAG;AAAA,IACH,QAAQ;AAAA,EACZ,CAAC;AACL;AAaA,IAAM,aAAa,OAAO,SAAS,EAAE,SAAS,MAAM,IAAI,CAAC,MAAM;AAE3D,MAAI,WAAW,QAAQ,KAAK;AACxB,UAAM,EAAE,IAAI,SAAS,QAAQ,IAAI,WAAW,OAAO;AACnD,UAAM,YAAY,qBAAqB,OAAO;AAC9C,UAAM,EAAE,GAAG,UAAU,GAAG,YAAY,GAAG,OAAO,GAAG,oBAAoB,GAAG,IAAI,IAAI,yBAAyB,OAAO;AAEhH,QAAI,YAAY;AACZ,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,aAAa,WAAW,OAAO,MAAM,oBAAoB,IAAI,WAAW,OAAO,IAAI;AACvF,QAAI,WAAW,MAAM;AACjB,mBAAa,MAAM,oBAAoB,IAAI,WAAW,SAAS,GAAG;AAAA,IACtE,WACS,WAAW,SAAS,CAAC,YAAY;AACtC,UAAI,SAAS,mBAAmB,eAAe,IAAI,QAAQ,GAAG;AAC1D,qBAAa,MAAM,oBAAoB,IAAI,WAAW,SAAS,GAAG;AAAA,MACtE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAKA,IAAM,WAAW,OAAO,SAAS,eAAe,EAAE,SAAS,MAAM,IAAI,CAAC,MAAM;AACxE,QAAM,kBAAkB,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAC7D,QAAM,gBAAgB,gBAAgB,IAAI,OAAO,mBAAmB;AAChE,UAAM,EAAE,UAAU,IAAI;AACtB,UAAM,EAAE,IAAI,SAAS,QAAQ,IAAI,WAAW,cAAc;AAC1D,UAAM,YAAY,qBAAqB,cAAc;AACrD,UAAM,EAAE,GAAG,oBAAoB,GAAG,SAAS,GAAG,KAAK,GAAG,WAAW,IAAI,yBAAyB,cAAc;AAE5G,QAAI,YAAY;AACZ;AAAA,IACJ;AACA,QAAI,OAAO;AACX,QAAI,KAAK,aAAa,GAAG;AACrB,UAAI,aAAa,WAAW,OAAO,MAAM,oBAAoB,IAAI,WAAW,OAAO,IAAI;AACvF,UAAI,WAAW,QACV,WAAW,SAAS,CAAC,cAAc,WAAW,mBAAmB,eAAe,IAAI,QAAQ,GAAI;AACjG,qBAAa,MAAM,oBAAoB,IAAI,WAAW,SAAS,GAAG;AAAA,MACtE;AACA,aAAO,cAAc,UAAU;AAC/B,UAAI,SAAS,gBAAgB;AACzB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,WAAW,IAAI;AAAA,MAClB,WAAW,QAAQ,oBAAoB,IAAI,WAAW,MAAM,mBAAmB,MAAM,GAAG,SAAS,SAAS;AAAA,MAC1G,WAAW,QAAS,WAAW,SAAS,UAClC,oBAAoB,IAAI,WAAW,MAAM,mBAAmB,eAAe,GAAG,SAAS,WAAW,GAAG,IACrG;AAAA,IACV,CAAC;AAAA,EACL,CAAC;AACD,SAAO,WAAW,IAAI,aAAa;AACvC;AAKA,IAAM,kBAAkB,OAAO,YAAY;AACvC,MAAI,CAAC,SAAS;AACV,UAAM,WAAW,IAAI,CAAC,sBAAsB,oBAAoB,GAAG,sBAAsB,oBAAoB,CAAC,CAAC;AAC/G;AAAA,EACJ;AACA,QAAM,kBAAkB,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAC7D,QAAM,gBAAgB,gBAAgB,IAAI,oBAAkB;AACxD,UAAM,EAAE,IAAI,SAAS,QAAQ,IAAI,WAAW,cAAc;AAC1D,UAAM,EAAE,GAAG,YAAY,GAAG,UAAU,IAAI,yBAAyB,cAAc;AAE/E,QAAI,YAAY;AACZ;AAAA,IACJ;AACA,UAAM,YAAY,qBAAqB,cAAc;AACrD,WAAO,WAAW,IAAI;AAAA,MAClB,uBAAuB,IAAI,WAAW,OAAO;AAAA,MAC7C,cAAc,kBAAkB,uBAAuB,IAAI,WAAW,OAAO,IAAI,eAAe;AAAA,IACpG,CAAC;AAAA,EACL,CAAC;AACD,QAAM,WAAW,IAAI,aAAa;AACtC;AAMA,IAAM,mBAAmB,OAAO,iBAAiB;AAG7C,QAAM,EAAE,aAAa,IAAI;AACzB,QAAM,EAAE,SAAS,QAAQ,IAAI,WAAW,YAAY;AACpD,QAAM,YAAY,qBAAqB,YAAY;AACnD,QAAM,EAAE,MAAM,WAAW,IAAI,UAAU,YAAY;AACnD,QAAM,wBAAwB;AAAA,IAC1B,QAAQ,CAAC,GAAG,sBAAsB,GAAG,oBAAoB;AAAA,IACzD,MAAM,CAAC,SAAS,OAAO;AAAA,IACvB,OAAO,CAAC;AAAA,EACZ,EAAE,YAAY;AACd,MAAI,yBAAyB,IAAI,qBAAqB,GAAG;AACrD,UAAM,WAAW,IAAI,QAAQ,uBAAuB,0BAAwB,+BAA+B,WAAW,YAAY,oBAAoB,CAAC,CAAC;AAAA,EAC5J;AACJ;AAEA,IAAM,mBAAmB,CAAC;AAO1B,SAAS,YAAY,gBAAgB,cAAc;AAC/C,MAAI,YAAY;AAChB,MAAI;AACJ,QAAM,6BAA6B,YAAY,YAAY,aAAW;AAClE,0CAAsC;AAAA,EAC1C,CAAC;AACD,QAAM,WAAW,YAAY;AACzB,UAAM,EAAE,gBAAgB,MAAM,WAAW,gBAAgB,YAAY,IAAI,WAAW,cAAc;AAClG,UAAM,YAAY,qBAAqB,cAAc;AACrD,UAAM,EAAE,GAAG,WAAW,GAAG,KAAK,GAAG,WAAW,GAAG,mBAAmB,IAAI,yBAAyB,cAAc;AAC7G,UAAM,EAAE,IAAI,SAAS,SAAS,UAAU,IAAI,WAAW,cAAc;AAErE,UAAM,EAAE,SAAS,IAAI,UAAU,cAAc;AAC7C,UAAM,EAAE,WAAW,gBAAgB,IAAI;AAEvC,QAAI,iBAAiB,OAAO,KAAK,QAAQ,IACnC,SAAS;AAAA;AAAA;AAAA,MAGP,eACM,iBACA,oBAAoB,IAAI,WAAW,OAAO;AAAA;AAExD,QAAI,cAAc,mBAAmB,CAAC,kBAAkB,CAAC,cAAc;AACnE,YAAM,iBAAiB,MAAM,uBAAuB,IAAI,WAAW,SAAS,GAAG;AAC/E,UAAI,gBAAgB;AAChB,cAAM,CAAC,YAAY,oBAAoB,IAAI;AAC3C,cAAM,oBAAoB,IAAI,WAAW,YAAY,sBAAsB,SAAS,eAAe;AACnG,yBAAiB;AAAA,MACrB;AAAA,IACJ;AAGA,UAAM,eAAe,YAAY,cAAc;AAG/C,UAAM,cAAc,YAAY;AAChC,UAAM,EAAE,SAAS,KAAK,QAAQ,MAAM,KAAK,IAAI;AAC7C,UAAM,EAAE,SAAS,CAAC,GAAG,UAAU,CAAC,GAAG,YAAY,OAAO,aAAa,IAAI,UAAU,YAAY;AAC7F,UAAM,6BAA8B,iBAAiB,EAAE,IAAI,iBAAiB,EAAE,KAAK,CAAC;AACpF,UAAM,cAAc,aAAa;AACjC,UAAM,uBAAuB,qBAAqB,WAAW;AAE7D,QAAI,sBAAsB,uBAAuB,iBAAiB,2BAA2B,SAAS;AACtG,QAAI,yBAAyB;AAC7B,QAAI,uBAAuB;AAC3B,QAAI,0BAA0B;AAE9B,QAAI,KAAK,SAAS,GAAG;AACjB,+BAAyB;AAAA,IAC7B,WACS,cAAc,SAAS,GAAG;AAC/B,YAAM,EAAE,WAAW,gBAAgB,SAAS,cAAc,YAAY,gBAAgB,IAAI;AAC1F,+BAAyB,KAAK,cAAc,IAAI,iBAAiB;AACjE,6BAAuB,KAAK,YAAY,IAAI,eAAe;AAC3D,gCAA0B,KAAK,eAAe,IAAI,kBAAkB;AAAA,IACxE;AAEA,QAAI,mBAAmB,gBAAgB;AACnC,0CAAoC;AAEpC,mBAAa,YAAY;AACzB,qBAAe,aAAa,kBAAkB,EAAE,gBAAgB,cAAc,WAAW,GAAG;AAC5F,8BAAwB,YAAY;AACpC,aAAO;AAAA,IACX;AACA,gBAAY;AACZ,QAAI,CAAC,gBAAgB,CAAC,qBAAqB;AAEvC,YAAM,QAAQ,eAAe;AAAA,QACzB,KAAK,kBAAkB,SAAS,QAAQ,MAAM;AAAA,QAC9C;AAAA,QACA;AAAA,QACA;AAAA,MACJ,GAAG,YAAY;AACf,4BAAsB,2BAA2B,SAAS,IAAI;AAAA,IAClE;AAEA,wCAAoC,mBAAmB;AAQvD,UAAM,qBAAqB,OAAO,gBAAgB,iBAAiB,gBAAgB,cAAc;AAC7F,YAAM,eAAe,MAAM;AAC3B,YAAM,kBAAkB,MAAM,UAAU,cAAc,mBAAmB,CAAC,CAAC;AAC3E,gBAAU,KAAK,cAAc;AAG7B,UAAI;AAEA,cAAM,iBAAiB,YAAY;AAAA,MACvC,SACO,IAAI;AAAA,MAAE;AAIb,YAAM,UAAU,CAAC,eAAe,CAAC;AAEjC,UAAI,WAAW,eAAe;AAC1B,YAAI;AACA,gBAAM,WAAW,IAAI;AAAA,YACjB,oBAAoB,IAAI,WAAW,iBAAiB,mBAAmB,MAAM,GAAG,SAAS,eAAe;AAAA,YACxG,aACI,oBAAoB,IAAI,WAAW,iBAAiB,mBAAmB,eAAe,GAAG,SAAS,iBAAiB,GAAG;AAAA,UAC9H,CAAC;AAAA,QACL,SACO,IAAI;AAAA,QAAE;AAAA,MACjB;AAGA,aAAO,UAAU,eAAe;AAAA,IACpC;AACA,WAAO,eAAe,YAAY,WAAW,IAAI,CAAC,oBAAoB,SAAS,GAAG,oBAAoB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,aAAa,UAAU,MAAM;AAE9I,iBAAW,4BAA4B,SAAS;AAChD,aAAO,mBAAmB,uBAAuB,aAAa,YAAY,GAAG,UAAU;AAAA,IAC3F,GAAG,CAAC,UAAU;AAEV,iBAAW,4BAA4B,SAAS;AAChD,aAAO,KAAK,oBAAoB;AAAA;AAAA,QAExB,mBAAmB,qBAAqB,OAAO,YAAY,GAAG,gBAAgB,UAAU;AAAA,UAC1F,cAAc,KAAK;AAAA,IAC7B,CAAC,GAAG,MAAM;AACN,8BAAwB,YAAY;AAAA,IACxC,CAAC;AAAA,EACL;AACA,SAAO;AAAA;AAAA,IAEH,OAAO,MAAM;AACT,kBAAY,4BAA4B,yBAAuB,uBAAuB,oBAAoB,MAAM,CAAC;AAAA,IACrH;AAAA,IACA,YAAY,CAAC,YAAY;AACrB,kBAAY,4BAA4B,yBAAuB,uBAAuB,oBAAoB,cAAc,oBAAoB,WAAW,OAAO,CAAC;AAAA,IACnK;AAAA,IACA,UAAU,CAAC,YAAY;AACnB,kBAAY,4BAA4B,yBAAuB,uBAAuB,oBAAoB,YAAY,oBAAoB,SAAS,OAAO,CAAC;AAAA,IAC/J;AAAA,IACA;AAAA,IACA,WAAW,MAAM;AAAA,EACrB;AACJ;AAEA,IAAM,mBAAmB,CAAC,YAAY,aAAa,MAAM;AACrD,QAAM,QAAQ,SAAS,QAAQ,UAAU;AACzC,WAAS,KAAK,SAAS,OAAO,OAAO,CAAC;AAC1C;AACA,IAAM,SAAN,MAAM,QAAO;AAAA,EACT,YAAY,MAAM,SAAS,KAAK,QAAQ,MAAM;AAC1C,SAAK,MAAM,CAAC;AACZ,SAAK,MAAM,CAAC;AACZ,SAAK,YAAY;AACjB,UAAM,eAAe,MAAM;AACvB,mBAAa,EAAE;AAAA,IACnB;AACA,iBAAa,IAAI;AACjB,WAAO,KAAK,YAAY;AACxB,UAAM,WAAW;AACjB,UAAM,iBAAiB,kBAAkB,OAAO;AAChD,aAAS,QAAQ;AACjB,aAAS,UAAU,eAAe,WAAW;AAC7C,aAAS,MAAM;AACf,aAAS,OAAO;AAChB,aAAS,UAAU;AAEnB,UAAM,sBAAsB,CAAC;AAC7B,UAAM,sBAAsB;AAC5B,UAAM,mBAAmB,cAAc,eAAe,mBAAmB,CAAC,IACpE,eAAe,mBAAmB,EAAE,IAAI,IACxC;AACN,UAAM,YAAY,UAAU,OAAO;AAEnC,YAAQ,CAAC,WAAW,cAAc,GAAG,eAAa;AAC9C,UAAI,eAAe,SAAS,MAAM,gBAAgB;AAC9C,4BAAoB,SAAS,IAAI,eAAe,SAAS;AAAA,MAC7D;AAAA,IACJ,CAAC;AAED,QAAI,qBAAqB,gBAAgB;AACrC,0BAAoB,mBAAmB,IAAI;AAAA,IAC/C;AAEA,QAAI,WAAW;AACX,eAAS,YAAY,QAAQ,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS,GAAG,gBAAc,WAAW,YAAY,OAAM,IAAI,qBAAqB,UAAU,IAAI,UAAU;AACvK,iBAAW,QAAQ,WAAW;AAAA,IAClC;AACA,aAAS,SAAS;AAAA,MACd,GAAG;AAAA,MACH,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,MACT,GAAI,UAAU,CAAC;AAAA,IACnB;AACA,aAAS,OAAO;AAChB,aAAS,OAAO,SAAS,OAAO,OAAO,SAAS;AAGhD,aAAS,MAAM,SAAS,YAAY;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,iBAAiB;AACxB,aAAS,KAAK,KAAK,eAAe;AAClC,WAAO,iBAAiB,iBAAiB,KAAK,GAAG;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,eAAe;AACpB,aAAS,KAAK,KAAK,aAAa;AAChC,WAAO,iBAAiB,eAAe,KAAK,GAAG;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,eAAe,YAAY;AAC5B,UAAM,WAAW;AACjB,UAAM,EAAE,UAAU,YAAY,UAAU,OAAO,UAAU,IAAI,YAAY,UAAU,YAAY;AAC/F,QAAI,SAAS,GAAG,IAAI,KAChB,WAAW,CAAC,QAAQ,UAAU,QAAQ,SAAS,KAAK,aAAW,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;AAC9F,QAAI,SAAS,GAAG,IAAI,KAAK,SAAS,CAAC,QAAQ,UAAU,QAAQ,SAAS,KAAK,aAAW,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;AAEjH,aAAS,MAAM,IAAI;AACnB,aAAS,YAAY;AACrB,aAAS,UAAU,YAAY,SAAS,GAAG,OAAK;AAC5C,eAAS,YAAY,UAAU;AAC/B,aAAO;AAAA,IACX,CAAC;AACD,WAAO,SAAS;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,MAAM;AACV,cAAU,IAAI,EAAE,OAAO;AAAA,EAC3B;AAAA,EACA,cAAc;AACV,WAAO,IAAI,IAAI;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,aAAa,YAAY;AAC1B,WAAO,YAAY,KAAK,KAAK,GAAG,aAAa,UAAU;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,YAAY;AACd,WAAO,aAAa,KAAK,KAAK,GAAG,UAAU;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,WAAW;AACf,WAAO,eAAe,KAAK,KAAK,GAAG,SAAS;AAAA,EAChD;AACJ;AAOA,IAAM,WAAW,aAAa;AAG9B,IAAM,oBAAoB;AAC1B,IAAM,gBAAgB,MAAM;AACxB,MAAI,UAAU,CAAC;AACf,QAAM,iBAAiB,mBAAmB;AAC1C,QAAM,UAAU;AAAA,IACZ,IAAID,MAAK,OAAO;AACZ,cAAQA,IAAG,IAAI;AACf,qBAAe,KAAK,mBAAmB,EAAE,MAAM,OAAO,KAAAA,MAAK,OAAO,WAAW,QAAQ,CAAC;AAAA,IAC1F;AAAA,IACA,KAAK,CAAAA,SAAO;AACR,YAAM,QAAQ,QAAQA,IAAG;AACzB,qBAAe,KAAK,mBAAmB,EAAE,MAAM,OAAO,KAAAA,MAAK,OAAO,WAAW,QAAQ,CAAC;AACtF,aAAO;AAAA,IACX;AAAA,IACA,OAAOA,MAAK;AACR,iBAAW,SAASA,IAAG;AACvB,qBAAe,KAAK,mBAAmB,EAAE,MAAM,UAAU,KAAAA,MAAK,WAAW,QAAQ,CAAC;AAAA,IACtF;AAAA,IACA,OAAO,MAAM;AACT,gBAAU,CAAC;AACX,qBAAe,KAAK,mBAAmB,EAAE,MAAM,SAAS,KAAK,IAAI,WAAW,QAAQ,CAAC;AAAA,IACzF;AAAA,IACA,SAAS;AAAA,EACb;AACA,SAAO;AACX;AACA,IAAM,sBAAsB,MAAM;AAC9B,QAAM,iBAAiB,mBAAmB;AAC1C,QAAM,WAAW;AACjB,QAAM,UAAU;AAAA,IACZ,KAAK,CAACA,MAAK,UAAU;AACjB,eAAS,QAAQA,MAAK,cAAc,KAAK,CAAC;AAC1C,qBAAe,KAAK,mBAAmB,EAAE,MAAM,OAAO,KAAAA,MAAK,OAAO,WAAW,SAAS,CAAC;AAAA,IAC3F;AAAA,IACA,KAAK,CAAAA,SAAO;AACR,YAAM,OAAO,SAAS,QAAQA,IAAG;AACjC,YAAM,QAAQ,OAAO,UAAU,IAAI,IAAI;AACvC,qBAAe,KAAK,mBAAmB,EAAE,MAAM,OAAO,KAAAA,MAAK,OAAO,WAAW,SAAS,CAAC;AACvF,aAAO;AAAA,IACX;AAAA,IACA,QAAQ,CAAAA,SAAO;AACX,eAAS,WAAWA,IAAG;AACvB,qBAAe,KAAK,mBAAmB,EAAE,MAAM,UAAU,KAAAA,MAAK,WAAW,SAAS,CAAC;AAAA,IACvF;AAAA,IACA,OAAO,MAAM;AACT,eAAS,MAAM;AACf,qBAAe,KAAK,mBAAmB,EAAE,MAAM,SAAS,KAAK,IAAI,WAAW,SAAS,CAAC;AAAA,IAC1F;AAAA,IACA,SAAS;AAAA,EACb;AACA,SAAO;AACX;AACA,IAAM,qBAAqB,MAAM;AAC7B,QAAM,0BAA0B,MAAM;AAClC,aAAS,YAAY,yBAAyB;AAAA,EAClD;AACA,SAAO;AAAA,IACH,KAAK,MAAM;AACP,8BAAwB;AAAA,IAC5B;AAAA,IACA,KAAK,MAAM;AACP,8BAAwB;AACxB,aAAO;AAAA,IACX;AAAA,IACA,QAAQ,MAAM;AACV,8BAAwB;AAAA,IAC5B;AAAA,IACA,OAAO,MAAM;AAAA,IAAE;AAAA,EACnB;AACJ;AAEA,IAAM,SAAS;AACf,IAAM,0BAAN,MAA8B;AAAA,EAC1B,YAAY,UAAU;AAIlB,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS;AACd,aAAS,YAAY,GAAG,qCAAqC;AAC7D,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,gBAAgB;AACjB,UAAM,EAAE,KAAK,IAAI,UAAU,cAAc;AACzC,UAAM,EAAE,SAAS,QAAQ,SAAS,IAAI;AACtC,QAAI,QAAQ,SAAS,UAAU;AAE3B,YAAM,kBAAmB,QAAQ,IAAI,IAAI,QAAQ,IAAI,KAAK,YAAY,MAAM;AAC5E,sBAAgB,IAAI,cAAc;AAElC,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,SAAS,WAAW,MAAM;AAE5B,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,cAAc;AAClB,QAAI,cAAc,OAAO,GAAG;AACxB,oBAAc,QAAQ;AACtB,qBAAe,QAAQ;AAAA,IAC3B;AACA,QAAI,WAAW,aAAa,SAAS,GAAG;AACpC,gBAAU;AAAA,IACd,WACS,SAAS,WAAW,GAAG;AAC5B,mBAAa;AAAA,IACjB;AACA,UAAM,EAAE,QAAQ,IAAI;AAEpB,QAAI,UAAU,YAAa,MAAO;AAElC,QAAI,YAAY;AACZ,gBAAU,QAAQ,UAAU,KAAK;AAAA,IACrC,WACS,SAAS;AACd,cAAQ,WAAW,WAAW,OAAO,GAAG,gBAAc,QAAQ,KAAK,UAAU,CAAC,GAAG,gBAAc;AAC3F,gBAAQ,UAAU,EAAE,QAAQ,YAAU,QAAQ,IAAI,MAAM,CAAC;AAAA,MAC7D,CAAC;AAAA,IACL;AACA,UAAM,mBAAmB,KAAK,YAAY,IAAI,WAAW,CAAC,GAAG,OAAO,GAAG,YAAY,IAAI,CAAC,GAAG,OAAO;AAClG,WAAQ,WAAW,mBAAmB,iBAAiB,CAAC;AAAA,EAC5D;AACJ;AAEA,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA,EAIxB,UAAU;AAAA,IACN,CAAC,OAAO,GAAG;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAA;AAAA;AAAA;AAAA,EAId,WAAW;AACf;AACA,IAAI,UAAU;AACd,IAAM,QAAN,MAAY;AAAA,EACR,YAAY,SAAS;AACjB,QAAI,IAAI;AACR,UAAM,WAAW;AACjB,aAAS,MAAM,QAAQ,OAAO,WAAW,IAAI,SAAS;AAEtD,aAAS,UAAU,QAAQ,WAAW,cAAc;AACpD,aAAS,UACL,QAAQ,YAAY,OAAO,iBAAiB,cAAc,oBAAoB,IAAI,mBAAmB;AAEzG,aAAS,UAAU;AAAA,MACf,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AACA,aAAS,YAAY,YAAa,0BAA2B,MAAM,KAAK,QAAQ,eAAe,QAAQ,OAAO,SAAS,KAAK,oBAAoB,eAAe,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,EACjM;AAAA,EACA,QAAQ,QAAQ;AACZ,WAAO,YAAa,QAAS,OAAO,UAAU,SAAS,MAAM,OAAO,KAAK,QAAQ,OAAO,IAAI;AAAA,EAChG;AAAA,EACA,IAAI,KAAK,QAAQ;AACb,WAAO,YAAa,QAAS,SAAS,MAAM,KAAK,MAAM;AAAA,EAC3D;AAAA,EACA,KAAK,KAAK,MAAM,QAAQ;AACpB,WAAO,YAAa,QAAS,UAAU,MAAM,KAAK,QAAQ,IAAI;AAAA,EAClE;AAAA,EACA,OAAO,KAAK,MAAM,QAAQ;AACtB,WAAO,YAAa,QAAS,YAAY,MAAM,KAAK,QAAQ,IAAI;AAAA,EACpE;AAAA,EACA,IAAI,KAAK,MAAM,QAAQ;AACnB,WAAO,YAAa,QAAS,SAAS,MAAM,KAAK,QAAQ,IAAI;AAAA,EACjE;AAAA,EACA,KAAK,KAAK,QAAQ;AACd,WAAO,YAAa,QAAS,UAAU,MAAM,KAAK,MAAM;AAAA,EAC5D;AAAA,EACA,MAAM,KAAK,MAAM,QAAQ;AACrB,WAAO,YAAa,QAAS,WAAW,MAAM,KAAK,QAAQ,IAAI;AAAA,EACnE;AAAA,EACA,QAAQ,KAAK,QAAQ;AACjB,WAAO,YAAa,QAAS,aAAa,MAAM,KAAK,MAAM;AAAA,EAC/D;AACJ;AACA,IAAI,kBAAkB;AACtB,IAAM,uBAAuB,CAAC;AAC9B,IAAM,uBAAuB,CAAC;AAM9B,IAAM,cAAc,CAAC,YAAY;AAC7B,QAAM,gBAAgB,YAAa,OAAQ,OAAO;AAClD,QAAM,gBAAgB,cAAc,QAAQ;AAC5C,MAAI,mBAAmB,eAAe;AAClC,aAAS,gBAAgB,SAAS,cAAc,MAAM,uCAAuC;AAAA,EACjG;AACA,oBAAkB;AAClB,QAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,GAAC,qBAAqB,SAAS,OAAO,KAAK,SAAS,sBAAsB,OAAO;AACjF,GAAC,qBAAqB,SAAS,OAAO,KAAK,SAAS,sBAAsB,OAAO;AACjF,SAAO;AACX;AAEA,IAAM,oBAAoB,MAAM;AAC5B,WAAS,iBAAiB,2CAA2C;AACrE,SAAO;AACX;", "names": ["expire", "key", "key", "key", "sourceKey"]}