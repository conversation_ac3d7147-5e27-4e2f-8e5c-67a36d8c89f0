!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("vue")):"function"==typeof define&&define.amd?define(["vue"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).vueHook=t(e.Vue)}(this,(function(e){"use strict";const t="undefined",n=!0;return typeof window===t&&typeof process!==t&&process.browser,{name:"Vue",create:t=>e.ref(t),dehydrate:e=>e.value,update:(e,t)=>{t.value=e},effectRequest({handler:t,removeStates:o,immediate:u,watchingStates:d}){var r;e.getCurrentInstance()&&e.onUnmounted(o),u&&t(),r=(o,u)=>{e.watch(o,(()=>{t(u)}),{deep:n})},(d||[]).forEach(r)},computed:t=>e.computed(t),watch:(t,o)=>{e.watch(t,o,{deep:n})},onMounted:t=>{e.getCurrentInstance()?e.onMounted(t):((e,t=0)=>{setTimeout(e,t)})(t,10)},onUnmounted:t=>{e.getCurrentInstance()&&e.onUnmounted(t)}}}));
