const RateLimiterRedis = require('./lib/RateLimiterRedis');
const RateLimiterMongo = require('./lib/RateLimiterMongo');
const RateLimiterMySQL = require('./lib/RateLimiterMySQL');
const RateLimiterPostgres = require('./lib/RateLimiterPostgres');
const {RateLimiterClusterMaster, RateLimiterClusterMasterPM2, RateLimiterCluster} = require('./lib/RateLimiterCluster');
const RateLimiterMemory = require('./lib/RateLimiterMemory');
const RateLimiterMemcache = require('./lib/RateLimiterMemcache');
const RLWrapperBlackAndWhite = require('./lib/RLWrapperBlackAndWhite');
const RateLimiterUnion = require('./lib/RateLimiterUnion');
const RateLimiterQueue = require('./lib/RateLimiterQueue');
const BurstyRateLimiter = require('./lib/BurstyRateLimiter');
const RateLimiterRes = require('./lib/RateLimiterRes');
const RateLimiterDynamo = require('./lib/RateLimiterDynamo');
const RateLimiterPrisma = require('./lib/RateLimiterPrisma');

module.exports = {
  RateLimiterRedis,
  RateLimiterMongo,
  RateLimiterMySQL,
  RateLimiterPostgres,
  RateLimiterMemory,
  RateLimiterMemcache,
  RateLimiterClusterMaster,
  RateLimiterClusterMasterPM2,
  RateLimiterCluster,
  RLWrapperBlackAndWhite,
  RateLimiterUnion,
  RateLimiterQueue,
  BurstyRateLimiter,
  RateLimiterRes,
  RateLimiterDynamo,
  RateLimiterPrisma,
};
