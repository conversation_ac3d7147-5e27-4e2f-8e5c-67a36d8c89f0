!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("vue-demi")):"function"==typeof define&&define.amd?define(["vue-demi"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).vueHook=t(e.Vue)}(this,(function(e){"use strict";const t="undefined",n=!0;return typeof window===t&&typeof process!==t&&process.browser,{name:"VueDemi",create:t=>e.ref(t),dehydrate:e=>e.value,update:(e,t)=>{t.value=e},effectRequest({handler:t,removeStates:o,immediate:d,watchingStates:u}){var i;e.getCurrentInstance()&&e.onUnmounted(o),d&&t(),i=(o,d)=>{e.watch(o,(()=>{t(d)}),{deep:n})},(u||[]).forEach(i)},computed:t=>e.computed(t),watch:(t,o)=>{e.watch(t,o,{deep:n})},onMounted:t=>{e.onMounted(t)},onUnmounted:t=>{e.onUnmounted(t)}}}));
