// 配置管理模块
class ConfigManager {
    constructor() {
        this.config = null;
        this.demoData = null;
    }

    // 加载配置文件
    async loadConfig() {
        try {
            const response = await fetch('./js/damoxing.json');
            this.config = await response.json();
            console.log('✅ 配置文件加载成功');
            return this.config;
        } catch (error) {
            console.error('❌ 配置文件加载失败:', error);
            throw new Error('无法加载模型配置文件');
        }
    }

    // 加载示例数据
    async loadDemoData() {
        try {
            const response = await fetch('./js/demo.json');
            this.demoData = await response.json();
            console.log('✅ 示例数据加载成功');
            return this.demoData;
        } catch (error) {
            console.error('❌ 示例数据加载失败:', error);
            throw new Error('无法加载示例数据文件');
        }
    }

    // 获取LM Studio配置
    getLMStudioConfig() {
        if (!this.config) {
            throw new Error('配置文件未加载');
        }
        return this.config.lmStudio;
    }

    // 获取预处理配置
    getPreprocessingConfig() {
        if (!this.config) {
            throw new Error('配置文件未加载');
        }
        return this.config.preprocessing;
    }

    // 获取关键词配置
    getKeywords() {
        if (!this.config) {
            throw new Error('配置文件未加载');
        }
        return this.config.keywords.security;
    }

    // 获取示例安全日志
    getExampleSecurityLog() {
        if (!this.demoData) {
            throw new Error('示例数据未加载');
        }
        return this.demoData.exampleSecurityLog;
    }

    // 生成分析提示词
    generateAnalysisPrompt(logData) {
        if (!this.config) {
            throw new Error('配置文件未加载');
        }
        
        const template = this.config.lmStudio.analysisPrompt;
        return template.replace('{logData}', logData);
    }

    // 获取完整的LM Studio请求配置
    getLMStudioRequestConfig(prompt) {
        const lmConfig = this.getLMStudioConfig();
        
        return {
            model: lmConfig.model,
            messages: [
                {
                    role: "system",
                    content: lmConfig.systemPrompt
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            ...lmConfig.defaultParams
        };
    }

    // 获取完整的API URL
    getAPIUrl() {
        const lmConfig = this.getLMStudioConfig();
        return lmConfig.baseURL + lmConfig.endpoint;
    }
}

// 导出配置管理器实例
export const configManager = new ConfigManager();
