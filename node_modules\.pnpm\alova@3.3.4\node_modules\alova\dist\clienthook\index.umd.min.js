!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("alova")):"function"==typeof define&&define.amd?define(["exports","alova"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).AlovaClientHook={},e.alova)}(this,(function(e,t){"use strict";const r="undefined",n=Promise,o=e=>n.resolve(e),s=e=>n.reject(e),a=Object,i=RegExp,c=void 0,l=null,u=!0,d=!1,h=(e,t,r)=>e.then(t,r),v=(e,t)=>e.catch(t),f=(e,t)=>e.finally(t),m=(e,t=0)=>setTimeout(e,t),p=e=>a.keys(e),g=e=>a.values(e),y=(e,t)=>e.forEach(t),E=(e,...t)=>e.push(...t),S=(e,t)=>e.map(t),b=(e,t)=>e.filter(t),w=e=>e.shift(),_=(e,t,r=0,...n)=>e.splice(t,r,...n),C=e=>e.length,R=e=>Array.isArray(e),x=(e,t)=>delete e[t],T=e=>typeof e,O=(e,t)=>e.test(`${t}`),k=(e,t)=>e.includes(t),M=(e,t=d)=>({value:e,writable:t}),P=(e,t,r,n=d)=>a.defineProperty(e,t,n?r:M(r,d));typeof window===r&&typeof process!==r&&process.browser;const j="memory",L=()=>{},N=e=>e,A=e=>"function"===T(e),D=e=>"number"===T(e)&&!Number.isNaN(e),U=e=>"string"===T(e),H=e=>e!==l&&"object"===T(e),F=e=>a.prototype.toString.call(e),$=e=>"[object Object]"===F(e),q=(e,t)=>e instanceof t,I=e=>e?e.getTime():Date.now(),B=e=>e.context,Q=e=>e.config,z=()=>{const e=(new Date).getTime();return Math.floor(Math.random()*e).toString(36)},G=e=>e.key,W=(e,t,r=[])=>{const n=A(e)?e(...r):e;return t(!!n.key,"hook handler must be a method instance or a function that returns method instance"),n},K=(e,...t)=>a.assign(e,...t),V=(e,...t)=>{const r={};for(const n in e)t.includes(n)||(r[n]=e[n]);return r};function J(){let e,t;return{promise:new Promise(((r,n)=>{e=r,t=n})),resolve:e,reject:t}}const X=e=>{const{cacheFor:t}=Q(e);let r=j,n=()=>0,o=d,s=c;const a=A(t);if(!a){let a=t;if($(t)){const{mode:e=j,expire:n,tag:i}=t||{};r=e,o="restore"===e,s=i?i.toString():c,a=n}n=t=>{return r=A(a)?a({method:e,mode:t}):a,D(r)?I()+r:I(r||c);var r}}return{f:t,c:a,e:n,m:r,s:o,t:s}},Y=(e,...t)=>new e(...t),Z=(e,t=[])=>A(e)?e(...t):e,ee=(e=d)=>{const t=[];let r=c,o=!1;return{addQueue:s=>Y(n,((n,a)=>{E(t,(()=>h(s(),n,(t=>{e?n(c):a(t)})))),o||(async()=>{for(o=u;C(t)>0;){const e=w(t);e&&await e()}r&&r(),o=d})()})),onComplete:e=>{r=e}}},te=(e,t,r=u,n,o)=>{const s=()=>{o&&n&&(e=t(e,n,o))!==o[n]&&(o[n]=e)};if(r&&s(),H(e))for(const n in e)q(e,String)||te(e[n],t,r,n,e);return!r&&s(),e},re=(e,t)=>{let{startQuiver:r,endQuiver:n}=e;const{delay:o,multiplier:s=1}=e;let a=(o||0)*s**(t-1);return(r||n)&&(r=r||0,n=n||1,a+=a*r+Math.random()*a*(n-r),a=Math.floor(a)),a};class ne extends Error{constructor(e,t,r){super(t+(r?`\n\nFor detailed: https://alova.js.org/error#${r}`:"")),this.name=`[alova${e?`/${e}`:""}]`}}const oe=(e="")=>(t,r,n)=>{if(!t)throw Y(ne,e,r,n)},se=JSON.parse,ae=()=>se.bridgeData||{},ie=()=>{const e={};return{eventMap:e,on(t,r){const n=e[t]=e[t]||[];return E(n,r),()=>{e[t]=b(n,(e=>e!==r))}},off(t,r){const n=e[t];if(n)if(r){const e=n.indexOf(r);e>-1&&n.splice(e,1)}else delete e[t]},emit(t,r){const n=e[t]||[];return S(n,(e=>e(r)))}}},ce=(e,t)=>{const r=ie(),n=z(),o=e((e=>r.emit(n,e)));return e=>(r.on(n,(r=>{t(e,r)})),o)};class le{constructor(e,t,r,n){this.s=e,this.k=t,this.$dhy=r,this.$exp=n}get v(){return this.$dhy(this.s)}get e(){return this.$exp(this.s)}}class ue extends le{constructor(e,t,r,n,o){super(e,t,r,n),this.$upd=o}set v(e){this.$upd(this.s,e)}get v(){return this.$dhy(this.s)}}const de={authRole:null},he={authRole:"login"},ve={authRole:"logout"},fe={authRole:"refreshToken"},me=({meta:e},t)=>{if($(e))for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)){const n=t[r];if(q(n,RegExp)?n.test(e[r]):e[r]===n)return u}return d},pe=(e,t)=>Y(n,(r=>{E(t,{method:e,resolve:r})})),ge=(e,t,r,n)=>{if(me(e,(null==t?void 0:t.metaMatches)||r)){return(A(t)?t:$(t)&&A(t.handler)?t.handler:L)(n,e)}},ye=async(e,t,r,o,s,a)=>{const i=C(o)>=2;let l=null==s?void 0:s.isExpired(...o);if(q(l,n)&&(l=await l),l)try{let n=u;if(i&&a&&(n=d,await pe(e,t)),n&&(r(u),await(null==s?void 0:s.handler(...o)),r(d),y(t,(({resolve:e})=>e()))),i){const{config:t}=e,r=t.transform;t.transform=c;const n=await e;return t.transform=r,n}}finally{r(d),_(t,0,C(t))}},Ee=e=>{let t=c,r=c,n=c;if(A(e))t=e;else if($(e)){const{onSuccess:o,onError:s,onComplete:a}=e;t=A(o)?o:t,r=A(s)?s:r,n=A(a)?a:n}return{onSuccess:t,onError:r,onComplete:n}},Se=e=>{throw e};const be=(e,t)=>{let r=l;return function(...n){const o=e.bind(this,...n),s=D(t)?t:t(...n);r&&(e=>{clearTimeout(e)})(r),s>0?r=m(o,s):o()}},we=(e,r=[])=>{const n=A(e)?e(...r):e;return oe("scene")(q(n,t.Method),"hook handler must be a method instance or a function that returns method instance"),n};var _e;function Ce(e,t={trackedKeys:{},bindError:d,initialRequest:d,...ae()}){const r=t=>e.ref?e.ref(t):{current:t};t=r(t).current;const n=r=>(e.export||N)(r,t),o=t=>{if(!A(e.memorize))return t;const r=e.memorize(t);return r.memorized=u,r},{dehydrate:s}=e,i=(r,n,o)=>r!==s(n,o,t)&&t.trackedKeys[o]&&e.update(r,n,o,t),c=e=>S(e,(e=>q(e,le)?e.e:e)),l=[],h={};return{create:(r,o)=>(E(l,o),Y(ue,e.create(r,o,t),o,(e=>s(e,o,t)),n,((e,t)=>i(t,e,o)))),computed:(r,o,a)=>(y(o,(e=>{e.k&&(h[e.k]=u)})),Y(le,e.computed(r,c(o),a,t),a,(e=>s(e,a,t)),n)),effectRequest:r=>e.effectRequest(r,t),ref:r,watch:(r,n)=>e.watch(c(r),n,t),onMounted:r=>e.onMounted(r,t),onUnmounted:r=>e.onUnmounted(r,t),memorize:o,__referingObj:t,exposeProvider:e=>{const r={},n={},s=[];for(const i in e){const c=e[i];if(A(c)&&!t.trackedKeys[i])r[i]=i.startsWith("on")?(...e)=>(c(...e),g):c.memorized?c:o(c);else{k(["uploading","downloading"],i)||i.startsWith("__")||E(s,i);const e=q(c,le);e&&(n[i]=c.s),a.defineProperty(r,i,{get:()=>(t.trackedKeys[i]=u,e?c.e:c),set:L,enumerable:u,configurable:u})}}const{update:c,__proxyState:v}=r;t.trackedKeys={...h},t.bindError=d;const{then:f}=r,m={__referingObj:t,update:o((e=>{p(e).forEach((t=>{k(l,t)?i(e[t],n[t],t):t in r&&A(c)&&c({[t]:e[t]})}))})),__proxyState:o((r=>k(l,r)&&q(e[r],le)?(t.trackedKeys[r]=u,e[r]):v(r))),then(e,r){y(s,(e=>{t.trackedKeys[e]=u}));const n=()=>{x(g,"then"),e(g)};A(f)?f(n,r):n()}},g=K(r,m);return g},objectify:(e,t)=>e.reduce(((e,r)=>(e[r.k]=t?r[t]:r,e)),{}),transformState2Proxy:(e,r)=>Y(ue,e,r,(e=>s(e,r,t)),n,((e,t)=>i(t,e,r)))}}!function(e){e[e.USE_REQUEST=1]="USE_REQUEST",e[e.USE_WATCHER=2]="USE_WATCHER",e[e.USE_FETCHER=3]="USE_FETCHER"}(_e||(_e={}));const Re=oe(""),xe=oe("useRequest"),Te=oe("useWatcher"),Oe=oe("useFetcher"),ke=e=>({[_e.USE_REQUEST]:xe,[_e.USE_WATCHER]:Te,[_e.USE_FETCHER]:Oe}[e]),Me=(e,r)=>e(q(r,t.Method),"expected a method instance."),Pe="success",je="error",Le="complete";class Ne{constructor(e,t){this.method=e,this.args=t}clone(){return{...this}}static spawn(e,t){return Y(Ne,e,t)}}class Ae extends Ne{constructor(e,t,r){super(e.method,e.args),this.data=t,this.fromCache=r}}class De extends Ne{constructor(e,t){super(e.method,e.args),this.error=t}}class Ue extends Ne{constructor(e,t,r,n,o){super(e.method,e.args),this.status=t,this.data=r,this.fromCache="error"!==t&&n,this.error=o}}class He{constructor(e,t,r){this.behavior=e,this.method=t,this.silentMethod=r}}class Fe extends He{constructor(e,t,r,n,o){super(e,t,r),this.queueName=n,this.retryTimes=o}}class $e extends Fe{constructor(e,t,r,n,o,s,a){super(e,t,r,n,o),this.data=s,this.vDataResponse=a}}class qe extends Fe{constructor(e,t,r,n,o,s,a){super(e,t,r,n,o),this.error=s,this.retryDelay=a}}class Ie extends Fe{constructor(e,t,r,n,o,s){super(e,t,r,n,o),this.error=s}}class Be extends He{constructor(e,t,r,n){super(e,t,r),this.args=n}}class Qe extends Be{constructor(e,t,r,n,o){super(e,t,r,n),this.data=o}}class ze extends Be{constructor(e,t,r,n,o){super(e,t,r,n),this.error=o}}class Ge extends Be{constructor(e,t,r,n,o,s){super(e,t,r,n),this.retryTimes=o,this.retryDelay=s}}class We extends Be{constructor(e,t,r,n,o,s,a){super(e,t,r,n),this.status=o,this.data=s,this.error=a}}class Ke extends Ne{constructor(e,t,r){super(e.method,e.args),this.retryTimes=t,this.retryDelay=r}}class Ve extends De{constructor(e,t,r){super(e,t),this.retryTimes=r}}const Je=(e,t)=>t(),Xe={},Ye=(e,t)=>{const r=Xe[e]||{};return r[t]?Array.from(r[t]):[]};function Ze(e,r,n=[]){const s=ke(e.ht);let a=W(r,s,n);const{fs:i,ht:l,c:v}=e,{loading:f,data:m,error:p}=i,g=l===_e.USE_FETCHER,{force:E=d,middleware:S=Je}=v,b=B(a),{id:w}=b,_=G(a),{abortLast:C=u}=v,R=!e.m;return e.m=a,(async()=>{let b=L,T=d,O=o(c),k=L,M=L;const P=await t.queryCache(a);let j=()=>!!P,N=d;g||(((e,t,r)=>{const n=Xe[e]=Xe[e]||{};n[t]||(n[t]=Y(Set)),n[t].add(r)})(w,_,e),b=()=>((e,t,r)=>{const n=Xe[e],o=n[t];n&&o&&(r?o.delete(r):o.clear(),0===o.size&&x(n,t))})(w,_,e));const A=t=>{T=u;const{force:r=E,method:o=a}=t||{},s=Z(r,[Y(Ne,a,n)]),c=e=>({loaded:t,total:r})=>{i[e].v={loaded:t,total:r}};a=o,e.rf[_]=b,N||(f.v=!!s||!P);const{downloading:l,uploading:h}=e.ro.trackedKeys;return k=l?a.onDownload(c("downloading")):k,M=h?a.onUpload(c("uploading")):M,O=a.send(s),j=()=>a.fromCache||d,O},D={method:a,cachedResponse:P,config:v,abort:()=>a.abort()},U=()=>l!==_e.USE_WATCHER||!C||e.m===a,H=(e=u)=>{e&&R&&(f.v=d),N=e},F=S(g?{...D,args:n,fetch:(t,...r)=>(Me(s,t),Ze(e,t,r)),proxyStates:V(i,"data"),controlLoading:H}:{...D,args:n,send:(...t)=>Ze(e,r,t),proxyStates:i,controlLoading:H},A);let $=c;const q=Ne.spawn(a,n);try{const t=await F,r=t=>(g?e.c.updateState!==d&&y(Ye(w,_),(e=>{e.fs.data.v=t})):U()&&(m.v=t),U()&&(p.v=c,!N&&(f.v=d),e.em.emit(Pe,Y(Ae,q,t,j())),e.em.emit(Le,Y(Ue,q,Pe,t,j(),c))),t);$=t!==c?r(t):T?await h(O,r,(()=>r(c))):c,!T&&!N&&(f.v=d)}catch(t){throw U()&&(p.v=t,!N&&(f.v=d),e.em.emit(je,Y(De,q,t)),e.em.emit(Le,Y(Ue,q,je,c,j(),t))),t}return k(),M(),$})()}const et=e=>e.current;function tt(e,r,o,s,a=d,i,l=0){var v;o={...o};let f=!!a,p=c;if(a)try{const t=W(r,ke(e)),s=B(t),a=s.l1Cache.get((E=s.id,S=G(t),"$a."+E+S));if(a&&!q(a,n)){const[e,t]=a;(!t||t>I())&&(p=e)}f=!!Z(null!==(v=o.force)&&void 0!==v?v:d)||!p}catch(e){}var E,S;const{create:b,effectRequest:w,ref:_,objectify:C,exposeProvider:x,transformState2Proxy:T,__referingObj:O}=Ce(t.promiseStatesHook(),o.__referingObj),k={total:0,loaded:0},{managedStates:M={}}=o,P=((e,t)=>{const r={};for(const n in e)r[n]=t(e[n],n,e);return r})(M,((e,t)=>T(e,t))),j=b(null!=p?p:A(s)?s():s,"data"),L=b(f,"loading"),N=b(c,"error"),U=b({...k},"downloading"),H=b({...k},"uploading"),F=C([j,L,N,U,H]),$=ie(),Q=et(_(((e,t,r,n)=>({m:c,rf:{},fs:{},em:r,ht:e,c:t,ro:n,ms:{}}))(e,o,$,O)));Q.fs=F,Q.em=$,Q.c=o,Q.ms={...F,...P};const z=i!==c,K=(e=r,t)=>Ze(Q,e,t),V=_(c),X=_(d),Y=et(_(((e=0)=>{let t=c;return r=>{t&&clearTimeout(t),t=m(r,e)}})())),ee=(e=O,r)=>{Y((()=>{t.globalConfigMap.ssr&&!et(V)||(O.initialRequest=X.current=u,h(K(r),(()=>{var e;null===(e=et(V))||void 0===e||e.resolve()}),(t=>{var r;if(!e.bindError&&!e.trackedKeys.error&&!et(V))throw t;null===(r=et(V))||void 0===r||r.reject(t)})))}))},te=_(be(((e,t,r)=>ee(t,r)),(e=>D(e)?R(l)?l[e]:l:0)));w({handler:z?e=>te.current(e,O,r):()=>ee(O),removeStates:()=>{y(g(Q.rf),(e=>e()))},frontStates:{...F,...P},watchingStates:i,immediate:null!=a?a:u});const re=x({...C([j,L,N,U,H]),abort:()=>Q.m&&Q.m.abort(),send:(e,t)=>K(t,e),onSuccess(e){$.on(Pe,e)},onError(e){O.bindError=u,$.on(je,e)},onComplete(e){$.on(Le,e)},then(e,t){const{promise:r,resolve:n,reject:o}=J();V.current={resolve:n,reject:o},m((()=>{!X.current&&n()}),10),h(r,(()=>{e(re)}),t)}});return re}function rt(e={}){const t=tt(_e.USE_FETCHER,L,e),{send:r}=t;return x(t,"send"),K(t,{fetch:(e,...t)=>(Me(Oe,e),r(t,e))})}function nt(e,t={}){const{immediate:r=u,initialData:n}=t,o=tt(_e.USE_REQUEST,e,t,n,!!r),{send:s}=o;return K(o,{send:(...e)=>s(e)})}function ot(e,t,r={}){Te(t&&C(t)>0,"expected at least one watching state");const{immediate:n,debounce:o=0,initialData:s}=r,a=tt(_e.USE_WATCHER,e,r,s,!!n,t,o),{send:i}=a;return K(a,{send:(...e)=>i(e)})}const st=oe("usePagination"),at=(e,t)=>st(D(e)&&e<C(t),"index must be a number that less than list length"),it=e=>[e[e.length-2],e[e.length-1],e.slice(0,e.length-2)];const ct=(e,t)=>oe(e)(R(t)&&C(t)>0,"please use an array to represent serial requests"),lt=(e,t,r=[])=>(e.shift(),(n,s)=>{null==t||t(n,(()=>o(c))),n.controlLoading();const a=n.proxyStates.loading;a.v=u;let i=s();for(const t of e)i=h(i,(e=>{const o=t(e,...n.args);return E(r,o),o.send()}));return i.finally((()=>{a.v=d}))});const ut="valueOf",dt="default",ht="silent";let vt;const ft=e=>{vt=e};let mt;let pt={};let gt=0;const yt=e=>{gt=e};let Et=[];const St=Symbol("GlobalSQBoot"),bt=Symbol("GlobalSQBefore"),wt=Symbol("GlobalSQSuccess"),_t=Symbol("GlobalSQError"),Ct=Symbol("GlobalSQFail"),Rt=ie(),xt=oe("useSQRequest");async function Tt(e,r){let o=d;if(e){const{update:s}=t.promiseStatesHook(),a=G(e),{id:i}=B(e),l=Ye(i,a),d=A(r)?{data:r}:r,h=S(l,(async r=>{let n=c;if(r){const{ms:e,ro:t}=r;y(p(d),(r=>{Re(r in e,`state named \`${r}\` is not found`);const o=e[r];let a=d[r](o.v);a=R(a)?[...a]:H(a)?{...a}:a,"data"===r&&(n=a),s(a,e[r].s,r,t)}))}n!==c&&await t.setCache(e,n)}));C(h)>0&&(await n.all(h),o=u)}return o}var Ot={forward:e=>q(e,Date)?e.getTime():c,backward:e=>Y(Date,e)},kt={forward:e=>q(e,RegExp)?e.source:void 0,backward:e=>Y(RegExp,e)};const Mt=(e={})=>{const t={date:Ot,regexp:kt,...e};return{serialize:e=>{if(H(e)){const{data:r}=te({data:e},(e=>{let r=c;const n=p(t).reduce(((e,n)=>{if(!r){const o=t[n].forward(e);o!==c&&(r=n,e=o)}return e}),e);return"[object Object]"===a.prototype.toString.call(e)?e={...e}:R(e)&&(e=[...e]),r!==c?[r,n]:e}));e=r}return e},deserialize:e=>H(e)?te({data:e},(e=>{if(R(e)&&2===C(e)){const r=t[e[0]];e=r?r.backward(e[1]):e}return e}),d).data:e}},Pt=Symbol("vdid"),jt=Symbol("original"),Lt=/\[vd:([0-9a-z]+)\]/,Nt=e=>{const t=null==e?void 0:e[Pt];t&&vt&&(vt[t]=c)},At=(e,t=u)=>{Nt(e);const r=null==e?void 0:e[Pt];return(r?`[vd:${r}]`:c)||(t?e:c)};function Dt(){return At(this)}const Ut=function(){};Ut.prototype=a.create(l,{[ut]:M(Dt)});const Ht=function(){};Ht.prototype=a.create(l,{[ut]:M(Dt)});var Ft=(e,t=z())=>{const r=(e,t=z())=>{if(e===l)e=Y(Ut);else if(e===c)e=Y(Ht);else{const t=a(e);P(t,ut,Dt),P(t,jt,e),e=t}return P(e,Pt,t),e},n=r(e,t);return($(n)||R(n))&&te(n,(e=>r(e))),n};const $t=(e,t=u)=>{const r=e=>(Nt(e),(null==e?void 0:e[Pt])&&(q(e,Ht)?e=c:q(e,Ut)?e=l:(q(e,Number)||q(e,String)||q(e,Boolean))&&(e=e[jt])),e),n=r(e);return t&&(H(n)||R(n))&&te(n,(e=>r(e))),n};const qt="__$k",It="__$v",Bt=()=>(xt(!!mt,"alova instance is not found, Do you forget to set `alova` or call `bootSilentFactory`?"),mt.l2Cache);let Qt=c;const zt="alova.SQ",Gt="alova.SM.",Wt=async(e,t)=>{const r=Bt();H(t)&&(t=te(R(t)?[...t]:{...t},((e,t,r)=>{var n;if(t===It&&r[qt])return e;if("context"===t&&"Alova"===(null===(n=null==e?void 0:e.constructor)||void 0===n?void 0:n.name))return c;const o=null==e?void 0:e[Pt];let s=$t(e,d);if("[object Object]"===F(s)?(e={...e},s={}):R(e)&&(e=[...e],s=[]),o){const t={[qt]:o,[It]:s,...e};if(q(e,String))for(let r=0;r<C(e);r+=1)null==t||delete t[r];e=t}return e}))),Qt=Qt||Mt(pt),await r.set(e,Qt.serialize(t))},Kt=async e=>{const t=await Bt().get(e);return Qt=Qt||Mt(pt),H(t)?te(Qt.deserialize(t),(e=>{if(H(e)&&(null==e?void 0:e[qt])){const t=e[qt],r=Ft(e[It],t);y(p(e),(t=>{k([qt,It],t)||(r[t]=e[t])})),e=r}return e}),d):t},Vt=async e=>{await Bt().remove(e)},Jt=e=>Wt(Gt+e.id,e),Xt=async(e,t,r)=>{const n=await Kt(zt)||{},o=n[e]||[],s=o.findIndex((e=>e===t));s>=0&&(r?(_(o,s,1,r.id),await Jt(r)):_(o,s,1),await Vt(Gt+t),C(o)<=0&&delete n[e],C(p(n))>0?await Wt(zt,n):await Vt(zt))};let Yt={};const Zt=(e,t)=>{const r=e=>{const r=At(e);return r in t?t[r]:U(e)?e.replace(Y(i,Lt.source,"g"),(e=>e in t?t[e]:e)):e};return H(e)&&!At(e,d)?te(e,r):e=r(e),e},er=(e,t)=>{let r={};const n=At(e,d);if(n&&(r[n]=t),H(e))for(const n in e)r={...r,...er(e[n],null==t?void 0:t[n])};return r},tr=(e,t)=>{t?e.active=t:delete e.active},rr=1e3,nr=(e,r)=>{const o=t=>{const r=e[0];if(r){const n=Et.find((({queue:e})=>q(e,i)?O(e,t):e===t)),o=()=>e[0]&&s(e[0]),a=(null==n?void 0:n.wait)?Z(n.wait,[r,t]):0;a&&a>0?m(o,a):o()}},s=(a,i=0)=>{tr(a,u);const{cache:c,id:l,behavior:v,entity:f,retryError:p=/.*/,maxRetryTimes:g=0,backoff:E={delay:rr},resolveHandler:b=L,rejectHandler:_=L,emitter:R,handlerArgs:x=[],virtualResponse:T,force:k}=a;Rt.emit(bt,Y(Fe,v,f,a,r,i)),h(f.send(k),(async s=>{if(w(e),c&&await Xt(r,l),b(s),v===ht){const o=er(T,s),{targetRefMethod:c,updateStates:l}=a;if(q(c,t.Method)&&l&&C(l)>0){const e={};y(l,(t=>{e[t]=e=>Zt(e,o)}));Tt(c,e)||await t.setCache(c,(e=>Zt(e,o)))}await((e,t)=>n.all(S(t,(async t=>{Zt(t.entity,e),t.cache&&await Jt(t)}))))(o,e),Rt.emit(wt,Y($e,v,f,a,r,i,s,o))}tr(a,d),o(r)}),(t=>{if(v!==ht)w(e),_(t);else{const e=e=>Rt.emit(_t,Y(qe,v,f,a,r,i,t,e)),{name:n="",message:o=""}=t||{};let c,l;q(p,RegExp)?l=p:H(p)&&(c=p.name,l=p.message);const u=c&&O(c,n)||l&&O(l,o);if(i<g&&u){const t=re(E,i+1);e(t),m((()=>{s(a,i+=1),R.emit("retry",Y(Ge,v,f,a,x,i,t))}),t)}else yt(2),e(),R.emit("fallback",Y(ze,v,f,a,x,t)),Rt.emit(Ct,Y(Ie,v,f,a,r,i,t))}tr(a,d)}))};o(r)},or=async(e,t,r=dt,n=()=>[])=>{e.cache=t;const o=Yt[r]=Yt[r]||[],s=C(o)<=0,a=!(await Promise.all(n())).some((e=>e===d));return a&&(t&&await(async(e,t)=>{await Jt(e);const r=await Kt(zt)||{},n=r[t]=r[t]||[];E(n,e.id),await Wt(zt,r)})(e,r),E(o,e),s&&1===gt&&nr(o,r)),a},sr=e=>{let t=c,r="",n=0;for(const o in Yt)if(n=Yt[o].indexOf(e),n>=0){t=Yt[o],r=o;break}return[t,r,n]};class ar{constructor(e,t,r,n=z(),o,s,a,i,c,l,u,d){const h=this;h.entity=e,h.behavior=t,h.id=n,h.emitter=r,h.force=!!o,h.retryError=s,h.maxRetryTimes=a,h.backoff=i,h.resolveHandler=c,h.rejectHandler=l,h.handlerArgs=u,h.vDatas=d}async save(){this.cache&&await Jt(this)}async replace(e){const t=this;xt(e.cache===t.cache,"the cache of new silentMethod must equal with this silentMethod");const[r,n,o]=sr(t);r&&(_(r,o,1,e),t.cache&&await Xt(n,t.id,e))}async remove(){const e=this,[t,r,n]=sr(e);t&&(_(t,n,1),e.cache&&await Xt(r,e.id))}setUpdateState(e,t="data"){e&&(this.targetRefMethod=e,this.updateStates=R(t)?t:[t])}}var ir=async()=>{const e=await Kt(zt)||{},r={},o=[];return y(p(e),(n=>{const s=r[n]=r[n]||[];E(o,...S(e[n],(async e=>{const r=await Kt(Gt+e);r&&E(s,(e=>{const{id:r,behavior:n,entity:o,retryError:s,maxRetryTimes:a,backoff:i,resolveHandler:c,rejectHandler:l,handlerArgs:d,targetRefMethod:h,force:v}=e,f=e=>{const{type:r,url:n,config:o,data:s}=e;return Y(t.Method,r,mt,n,o,s)},m=Y(ar,f(o),n,ie(),r,v,s,a,i,c,l,d);return m.cache=u,h&&(m.targetRefMethod=f(h)),y(p(e),(t=>{k(["id","behavior","emitter","entity","retryError","maxRetryTimes","backoff","resolveHandler","rejectHandler","handlerArgs","targetRefMethod","force"],t)||(m[t]=e[t])})),m})(r))})))})),await n.all(o),r};let cr=c;var lr=(e,t)=>{const{behavior:r="queue",queue:s=dt,retryError:a,maxRetryTimes:i,backoff:l}=t||{},v=ie();let f,m,g,y=dt,E=d;return{c:(...t)=>(xt(A(e),"method handler must be a function. eg. useSQRequest(() => method)"),ft({}),f=t,e(...t)),m:({method:e,args:t,cachedResponse:S,proxyStates:b,config:w},_)=>{const{silentDefaultResponse:R,vDataCaptured:x,force:T=d}=w,k=Ne.spawn(e,t);m=Z(r,[k]),y=Z(s,[k]),E=Z(T,[k]);const M=()=>{ft(f=c)};if(A(x)){let t=vt&&C(p(vt))>0;if(!t){const{url:r,data:n}=e,{params:o,headers:s}=Q(e);te({url:r,params:o,data:n,headers:s},(e=>(t||!At(e,d)&&!O(Lt,e)||(t=u),e)))}const r=t?x(e):c;if(r!==c)return M(),o(r)}if("static"!==m){const r=()=>{const r=Y(n,((t,r)=>{g=Y(ar,e,m,v,c,!!E,a,i,l,t,r,f,vt&&p(vt)),M()}));return h(o(c),(async()=>{const r=()=>Y(Be,m,e,g,t);await or(g,C(v.eventMap.fallback||[])<=0&&m===ht,y,(()=>v.emit("beforePushQueue",r())))&&v.emit("pushedQueue",r())})),r};if("queue"===m){const e=E||!S;return e&&(b.loading.v=u),e?r():h(o(S))}const s=r(),d=g.virtualResponse=Ft(A(R)?R():c);return h(s,(e=>{b.data.v=e})),o(d)}return M(),_()},d:e=>{e.onSuccess=ce(e.onSuccess,((e,t)=>{cr=g,e(Y(Qe,m,t.method,g,t.args,t.data))})),e.onError=ce(e.onError,((e,t)=>{e(Y(ze,m,t.method,g,t.args,t.error))})),e.onComplete=ce(e.onComplete,((e,t)=>{e(Y(We,m,t.method,g,t.args,t.status,t.data,t.error))}))},b:{onFallback:e=>{v.on("fallback",e)},onBeforePushQueue:e=>{v.on("beforePushQueue",e)},onPushedQueue:e=>{v.on("pushedQueue",e)},onRetry:e=>{v.on("retry",e)}}}};const ur=async(e,t=dt,r=d)=>{const n=(t=[])=>t.filter((t=>{if(e===c)return u;const n=Q(t.entity).name||"";return(q(e,RegExp)?O(e,n):n===e)&&(r?t.active:u)}));return[...n(Yt[t]),...0===gt?n((await ir())[t]):[]]};class dr{constructor(e,t={}){var r,n,o;this.cancelBubble=d,this.currentTarget=l,this.defaultPrevented=d,this.eventPhase=0,this.isTrusted=d,this.returnValue=u,this.srcElement=l,this.target=l,this.NONE=0,this.CAPTURING_PHASE=1,this.AT_TARGET=2,this.BUBBLING_PHASE=3,this.type=e,this.bubbles=null!==(r=t.bubbles)&&void 0!==r&&r,this.cancelable=null!==(n=t.cancelable)&&void 0!==n&&n,this.composed=null!==(o=t.composed)&&void 0!==o&&o,this.timeStamp=Date.now()}preventDefault(){this.cancelable&&(this.defaultPrevented=!0)}stopImmediatePropagation(){}stopPropagation(){this.cancelBubble=!0}composedPath(){return[]}initEvent(e,t,r){}}dr.NONE=0,dr.CAPTURING_PHASE=1,dr.AT_TARGET=2,dr.BUBBLING_PHASE=3;const hr="undefined"!=typeof Event?Event:dr;class vr extends hr{constructor(e,t){super(e,{bubbles:u,cancelable:u,composed:u}),this.data=t.data,this.lastEventId=t.lastEventId,this.origin=t.origin||"",this.error=t.error}}class fr extends Ne{constructor(e,t){super(e.method,e.args),this.eventSource=t}}class mr extends fr{constructor(e,t){super(e,e.eventSource),this.error=t}}class pr extends fr{constructor(e,t){super(e,e.eventSource),this.data=t}}const gr=oe("EventSourceFetch");class yr{constructor(e,t,r={}){this.CONNECTING=0,this.OPEN=1,this.CLOSED=2,this.onopen=null,this.onmessage=null,this.onerror=null,this._listeners={},this._reconnectTime=null,this._controller=null,this._lastEventId="",this._origin="",this.url=e,this.readyState=yr.CONNECTING,this._options={...r},this._reconnectTime=t;const n=Y(URL,e,window.location.href);this._origin=n.origin,e&&m((()=>this._connect()))}addEventListener(e,t){this._listeners[e]=this._listeners[e]||[];this._listeners[e].find((e=>e===t||H(e)&&H(t)&&(null==e?void 0:e.handleEvent)===t.handleEvent))||this._listeners[e].push(t)}removeEventListener(e,t){t&&this._listeners[e]&&(this._listeners[e]=b(this._listeners[e],(e=>e!==t&&!("object"==typeof e&&"object"==typeof t&&(null==e?void 0:e.handleEvent)===t.handleEvent))))}dispatchEvent(e){if(!(e instanceof vr))return u;const t=this._listeners[e.type]||[];for(const r of t)A(r)?r(e):r&&A(r.handleEvent)&&r.handleEvent(e);const r=this[`on${e.type}`];return A(r)&&r(e),!e.defaultPrevented}close(){this.readyState!==yr.CLOSED&&(this.readyState=yr.CLOSED,this._dispatchEvent("close",""),this._controller&&(this._controller.abort(),this._controller=null))}_connect(){if(this.readyState===yr.CLOSED)return;this._controller=Y(AbortController);const e=this._options,t=e.headers||{},r=["Accept","text/event-stream"],n="Last-Event-ID",s=this._lastEventId;if(R(t))E(t,r),s&&E(t,[n,s]);else if(q(t,Headers))t.append(r[0],r[1]),s&&t.append(n,s);else if(H(t)){const[e,o]=r;t[e]=o,s&&(t[n]=s)}const a={...e,headers:t,signal:this._controller.signal};fetch(this.url,a).then((e=>{gr(e.ok,`HTTP error: ${e.status}`),gr(e.body,"ReadableStream not supported");const t=Y(URL,e.url);this._origin=t.origin,this.readyState=yr.OPEN,this._dispatchEvent("open","");const r=e.body.getReader(),n=new TextDecoder;let s="";const a=({done:e,value:t})=>{if(e)return this.readyState!==yr.CLOSED&&this._reconnect(),o();s+=n.decode(t,{stream:!0});const i=s.split(/\r\n|\r|\n/);return s=i.pop()||"",this._processEventStream(i),r.read().then(a).catch((e=>("AbortError"!==e.name&&this.readyState!==yr.CLOSED&&this._onError(e),o())))};return r.read().then(a).catch((e=>{"AbortError"!==e.name&&this.readyState!==yr.CLOSED&&this._onError(e)}))})).catch((e=>{"AbortError"!==e.name&&this.readyState!==yr.CLOSED&&this._onError(e)}))}_processEventStream(e){let t="message",r="",n=null,o=null;const s=()=>{if(r){if(r.endsWith("\n")&&(r=r.substring(0,r.length-1)),null!==n&&(this._lastEventId=n),null!==o&&null===this._reconnectTime){const e=parseInt(o,10);Number.isNaN(e)||(this._reconnectTime=e)}this._dispatchEvent(t,r)}t="message",r="",n=null,o=null};for(const a of e){if(""===a){s();continue}if(a.startsWith(":"))continue;let e,i;const c=a.indexOf(":");switch(-1===c?(e=a,i=""):(e=a.slice(0,c),i=a.slice(c+(" "===a[c+1]?2:1))),e){case"event":t=i;break;case"data":r=r?`${r}\n${i}`:i;break;case"id":if(i.includes("\0"))continue;n=i;break;case"retry":o=i;break;default:throw Y(ne,"EventSource",`EventSource: Unknown field "${e}", ignoring`)}}}_dispatchEvent(e,t){const r=Y(vr,e,{type:e,data:t,lastEventId:this._lastEventId,origin:this._origin});this.dispatchEvent(r)}_onError(e){const t=Y(vr,"error",{type:"error",data:"",lastEventId:this._lastEventId,origin:this._origin,error:e});this.dispatchEvent(t),this.readyState!==yr.CLOSED&&this._reconnect()}_reconnect(){var e;if(null!==this._reconnectTime&&this._reconnectTime<=0)this.close();else if(this.readyState!==yr.CLOSED){this.readyState=yr.CONNECTING;const t=null!==(e=this._reconnectTime)&&void 0!==e?e:1e3;m((()=>this._connect()),t)}}}yr.CONNECTING=0,yr.OPEN=1,yr.CLOSED=2;const Er=Symbol("SSEOpen"),Sr=Symbol("SSEMessage"),br=Symbol("SSEError");var wr;!function(e){e[e.CONNECTING=0]="CONNECTING",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(wr||(wr={}));const _r="useSSE",Cr=oe(_r);var Rr;!function(e){e.Open="open",e.Error="error",e.Message="message",e.Close="close"}(Rr||(Rr={}));const xr=oe("useUploader");function Tr(e,{limit:r=0,localLink:o,replaceSrc:s,mode:a}={}){const{create:i,computed:l,exposeProvider:h,ref:v}=Ce(t.promiseStatesHook()),m=ie(),p=i([],"fileList"),g=l((()=>p.v[0]),[p],"file"),w=i(!1,"uploading"),x=i(0,"successCount"),T=i(0,"failCount"),O=l((()=>({...p.v.reduce(((e,{progress:t,status:r})=>(0!==r&&(e.total+=t.total,e.uploaded+=t.uploaded),e)),{uploaded:0,total:0})})),[p],"progress"),M=l((()=>{var e;return null===(e=p.v.find((e=>e.error)))||void 0===e?void 0:e.error}),[p],"error"),P=(e,t=[1,2],r="upload")=>{const n={upload:"uploaded",abort:"aborted"}[r],o=p.v;return C(e)>0?S(e,((e,r)=>{const s=D(e),a=s?o[e]:e,i=s?`index ${e}`:`position ${r}`;return xr(a,`The file of ${i} does not exist`),C(t)>0&&xr(!k(t,a.status)&&H(a.file),`The file of ${i} cannot be ${n}, which status is ${a.status}`),a})):b(o,(e=>!k(t,e.status)))},j=(e,t)=>b(e,(e=>C(t)>0?k(t,e.status):u)),L=v([]),N=(e,t)=>{p.v.findIndex((t=>t.file===e))>-1&&E(L.current,{f:e,m:t})},A=e=>{L.current=b(L.current,(({m:t})=>t!==e))},U=(...e)=>{const t=P(e,[0,2,3],"abort"),r=[];C(t)>0?y(t,(({file:e})=>{const t=L.current.find((({f:t})=>t===e));t&&E(r,t.m)})):E(r,...S(L.current,(({m:e})=>e)));const n=[];y(r,(e=>{n.includes(e)||E(n,e)})),y(n,(e=>e.abort()))},F=(e,t,r)=>{const n=Ne.spawn(e,[]);return{successEvent:Y(Ae,n,t,d),errorEvent:Y(De,n,r),completeEvent:Y(Ue,n,r?je:Pe,t,d,r)}},$=(e,t,r,n,o=0)=>{if(e.status=t,2===t){if(e.progress.uploaded=e.progress.total,s){const t=s(r,o);t&&(e.src=t)}}else 3===t&&(e.error=n);p.v=[...p.v]};return h({fileList:p,uploading:w,file:g,progress:O,successCount:x,failCount:T,error:M,appendFiles:async(e={},t={})=>{var s;let a=e,i=t;e.file||R(e)||(i=e,a=[]);let c=R(a)?a:[a];C(c)<=0&&(c=await Tr.selectFile(i)),xr(r<=0||C(p.v)+C(c)<=r,`The number of files exceeds the limit of ${r}`);const{converters:l}=Tr,u=await n.all(S(c,(async e=>{const t=l.find((({is:t})=>t(e)));xr(t,`Invalid file type, only ${S(l,(({name:e})=>e)).join(", ")} are supported, if other file needs, customize convert function with \`useUploader.convertFile.push({ is: ..., convert: ... })\``);const r=await t.convert(e);return xr(r,"Failed to convert file"),{src:e.src||(o?URL.createObjectURL(r):void 0),file:r,status:0,progress:{uploaded:0,total:r.size}}}))),d=null!==(s=i.start)&&void 0!==s?s:C(p.v),h=b(u,Boolean),v=[...p.v];return _(v,d,0,...h),p.v=v,C(u)},removeFiles:(...e)=>{const t=P(e,[]);C(t)>0?(U(...j(t,[1])),p.v=b(p.v,(e=>!k(t,e))),x.v=C(j(p.v,[2])),T.v=C(j(p.v,[3]))):(U(...j(p.v,[1])),p.v=[])},upload:async(...t)=>{const r=P(t);y(r,(e=>{e.status=1}));const o=S(r,(({file:e})=>({file:e,name:e.name})));return w.v=u,"batch"===a?((t,r)=>{const n=e(r);return n.onUpload((e=>{y(t,(t=>{t.progress.uploaded=e.loaded*(t.progress.total/e.total)}))})),y(t,(({file:e})=>N(e,n))),n.then((e=>{y(t,((t,r)=>$(t,2,e,void 0,r))),x.v=C(j(p.v,[2]));const{successEvent:r,completeEvent:o}=F(n,e);return m.emit(Pe,r),m.emit(Le,o),e}),(e=>{y(t,(t=>$(t,3,void 0,e))),T.v=C(j(p.v,[3]));const{errorEvent:r,completeEvent:o}=F(n,void 0,e);return m.emit(je,r),m.emit(Le,o),e})).finally((()=>{w.v=!1,A(n)}))})(r,o):(t=>{const r=S(t,((r,n)=>{const o=e({file:r.file,name:r.file.name});return o.onUpload((({loaded:e,total:r})=>{y(t,(t=>{t.progress.uploaded=e,t.progress.total=r}))})),N(r.file,o),o.then((e=>{$(r,2,e,c,n),x.v+=1;const{successEvent:t,completeEvent:s}=F(o,e);return m.emit(Pe,t),m.emit(Le,s),e}),(e=>{$(r,3,c,e),T.v+=1;const{errorEvent:t,completeEvent:n}=F(o,c,e);return m.emit(je,t),m.emit(Le,n),e})).finally((()=>{A(o)}))}));return f(n.all(r),(()=>{w.v=d}))})(r)},abort:U,onSuccess:e=>{m.on(Pe,e)},onError:e=>{m.on(je,e)},onComplete:e=>{m.on(Le,e)}})}Tr.selectFile=({multiple:e,accept:t}={})=>{const r=document.createElement("input");return r.type="file",r.multiple=!!e,t&&(r.accept=t),r.click(),Y(Promise,(e=>{r.addEventListener("change",(()=>{const t=S(Array.from(r.files||[]),(e=>({file:e,name:e.name,mimeType:e.type})));e(t)}))}))};const Or="text/plain";Tr.converters=[{name:"HTMLCanvasElement",is:({file:e})=>q(e,HTMLCanvasElement),async convert({file:e,mimeType:t,name:r}){const o=await Y(n,(t=>null==e?void 0:e.toBlob(t)));if(o)return Y(File,[o],r||"image.png",{type:t||o.type})}},{name:"base64",is:({file:e})=>U(e),convert({file:e="",mimeType:t,name:r}){var n;xr(/data:.+;base64,/.test(e),"Invalid base64 string");const o=e.split(","),s=null===(n=o[0].match(/:(.*?);/))||void 0===n?void 0:n[1],a=atob(o[1]),i=new Uint8Array(C(a));return y(Array.from(a),((e,t)=>{i[t]=e.charCodeAt(0)})),Y(File,[i],r||"file",{type:t||s||Or})}},{name:"File",is:({file:e})=>q(e,File),convert:({file:e})=>e},{name:"Blob",is:({file:e})=>q(e,Blob),convert:({file:e,name:t,mimeType:r})=>Y(File,[e],t||"file",{type:r||e.type||Or})},{name:"ArrayBuffer",is:({file:e})=>q(e,ArrayBuffer),convert:({file:e,name:t,mimeType:r})=>Y(File,[Y(Blob,[e])],t||"file",{type:r||Or})}];const kr=(e,r={})=>{let n=u;const{enableFocus:o=u,enableVisibility:s=u,enableNetwork:a=u,pollingTime:i=0,throttle:c=1e3}=r,{onMounted:l,onUnmounted:h,__referingObj:v}=Ce(t.promiseStatesHook()),f=nt(e,{...r,__referingObj:v}),m=()=>{n&&(f.send(),c>0&&(n=d,setTimeout((()=>{n=u}),c)))};let p=L,g=L,y=L,E=L;return l((()=>{t.globalConfigMap.ssr||(p=a?kr.onNetwork(m,r):p,g=o?kr.onFocus(m,r):g,y=s?kr.onVisibility(m,r):y,E=i>0?kr.onPolling(m,r):E)})),h((()=>{p(),g(),y(),E()})),f},Mr=(e,t)=>(window.addEventListener(e,t),()=>window.removeEventListener(e,t));kr.onNetwork=e=>Mr("online",e),kr.onFocus=e=>Mr("focus",e),kr.onVisibility=e=>Mr("visibilitychange",(()=>"visible"===document.visibilityState&&e())),kr.onPolling=(e,t)=>{const r=setInterval(e,t.pollingTime);return()=>clearInterval(r)};const Pr="useCaptcha",jr=oe(Pr);const Lr=Symbol("FormRestore"),Nr=ie(),Ar={},Dr=e=>{const t=e=>R(e)?[...e]:$(e)?{...e}:e;return te(t(e),t)};const Ur=Symbol("RetriableRetry"),Hr=Symbol("RetriableFail"),Fr="useRetriableRequest",$r=oe(Fr);let qr=0;const Ir={},Br=oe("subscriber");e.accessAction=(e,t,r=!1)=>{const n=[];"symbol"==typeof e||U(e)||D(e)?Ir[e]&&E(n,...g(Ir[e])):q(e,RegExp)&&y(b(p(Ir),(t=>e.test(t))),(e=>{E(n,...g(Ir[e]))})),0!==n.length||r||Br(!1,`no handler can be matched by using \`${e.toString()}\``),y(b(n,N),t)},e.actionDelegationMiddleware=e=>{const{ref:r,onUnmounted:n}=Ce(t.promiseStatesHook()),o=r(qr+1);return o.current>qr&&(qr+=1),n((()=>{var t;(null===(t=Ir[e])||void 0===t?void 0:t[o.current])&&delete Ir[e][o.current]})),(t,r)=>{const{abort:n,proxyStates:s,delegatingActions:a={}}=t,i=e=>{for(const t in e)s[t]&&(s[t].v=e[t])},c=Ir[e]=Ir[e]||[],l=(e=>!!e.send)(t)?{...a,send:t.send,abort:n,update:i}:{...a,fetch:t.fetch,abort:n,update:i};return c[o.current]=l,r()}},e.bootSilentFactory=e=>{if(0===gt){const{alova:t,delay:r=500}=e;mt=t,((e={})=>{pt=e})(e.serializers),((e=0)=>{Et=R(e)?e:[{queue:dt,wait:e}]})(e.requestWait),m((async()=>{var e;e=await ir(),y(p(e),(t=>{const r=Yt[t]=Yt[t]||[];E(r,...e[t])})),y(p(Yt),(e=>{nr(Yt[e],e)})),yt(1),Rt.emit(St,c)}),r)}},e.createClientTokenAuthentication=({visitorMeta:e,login:t,logout:r,refreshToken:n,assignToken:o=L})=>{let s=d;const a=[];return{waitingList:a,onAuthRequired:r=>async i=>{const c=me(i,e||de),l=me(i,(null==t?void 0:t.metaMatches)||he);return c||l||me(i,(null==n?void 0:n.metaMatches)||fe)||(s&&await pe(i,a),await ye(i,a,(e=>{s=e}),[i],n)),c||l||await o(i),null==r?void 0:r(i)},onResponseRefreshToken:e=>{const n=Ee(e);return{...n,onSuccess:async(e,o)=>(await ge(o,t,he,e),await ge(o,r,ve,e),(n.onSuccess||N)(e,o))}}}},e.createServerTokenAuthentication=({visitorMeta:e,login:t,logout:r,refreshTokenOnSuccess:n,refreshTokenOnError:o,assignToken:s=L})=>{let a=d;const i=[];return{waitingList:i,onAuthRequired:r=>async c=>{const l=me(c,e||de),u=me(c,(null==t?void 0:t.metaMatches)||he);return l||u||me(c,(null==n?void 0:n.metaMatches)||fe)||me(c,(null==o?void 0:o.metaMatches)||fe)||a&&await pe(c,i),l||u||await s(c),null==r?void 0:r(c)},onResponseRefreshToken:s=>{const c=Ee(s);return{...c,onSuccess:async(o,s)=>{if(!me(s,e||de)&&!me(s,(null==t?void 0:t.metaMatches)||he)&&!me(s,(null==n?void 0:n.metaMatches)||fe)){const e=await ye(s,i,(e=>{a=e}),[o,s],n,a);if(e)return e}return await ge(s,t,he,o),await ge(s,r,ve,o),(c.onSuccess||N)(o,s)},onError:async(r,n)=>{if(!me(n,e||de)&&!me(n,(null==t?void 0:t.metaMatches)||he)&&!me(n,(null==o?void 0:o.metaMatches)||fe)){const e=await ye(n,i,(e=>{a=e}),[r,n],o,a);if(e)return e}return(c.onError||L)(r,n)}}}}},e.dehydrateVData=e=>$t(e),e.equals=(e,t)=>e===t?u:At(e)===At(t),e.filterSilentMethods=ur,e.getSilentMethod=async(e,t=dt,r=d)=>(await ur(e,t,r))[0],e.isVData=e=>!!At(e,d)||O(Lt,e),e.onBeforeSilentSubmit=e=>Rt.on(bt,e),e.onSilentSubmitBoot=e=>Rt.on(St,e),e.onSilentSubmitError=e=>Rt.on(_t,e),e.onSilentSubmitFail=e=>Rt.on(Ct,e),e.onSilentSubmitSuccess=e=>Rt.on(wt,e),e.silentQueueMap=Yt,e.statesHookHelper=Ce,e.stringifyVData=At,e.updateState=Tt,e.updateStateEffect=async(e,t)=>(cr&&(cr.setUpdateState(e,A(Tt)?c:p(Tt)),await cr.save()),Tt(e,t)),e.useAutoRequest=kr,e.useCaptcha=(e,r={})=>{const{initialCountdown:o,middleware:s}=r;jr(o===c||o>0,"initialCountdown must be greater than 0");const{create:a,ref:i,objectify:l,exposeProvider:u,__referingObj:h}=Ce(t.promiseStatesHook()),v=a(0,"countdown"),f=nt(e,{...r,__referingObj:h,immediate:d,managedStates:l([v],"s"),middleware:s?(e,t)=>s({...e,send:p},t):c}),m=i(c),p=(...e)=>Y(n,((t,n)=>{v.v<=0?f.send(...e).then((e=>{v.v=r.initialCountdown||60,m.current=setInterval((()=>{v.v-=1,v.v<=0&&clearInterval(m.current)}),1e3),t(e)})).catch((e=>n(e))):n(Y(ne,Pr,"the countdown is not over yet"))}));return u({...f,send:p,...l([v])})},e.useFetcher=rt,e.useForm=(e,r={})=>{const{id:n}=r,o=n?Ar[n]:c,{id:s,initialForm:a,store:i,resetAfterSubmiting:l,immediate:h=d,middleware:v}=(null==o?void 0:o.config)||r;t.promiseStatesHook();const{create:f,ref:m,onMounted:p,onUnmounted:g,watch:y,objectify:E,exposeProvider:S,__referingObj:b}=Ce(t.promiseStatesHook()),w=$(i),_=w?i.enable:i,C=f(Dr((null==o?void 0:o.form)||a),"form"),R=e,x=ie(),T=m(c);let O=null==o?void 0:o.l2Cache;_&&!(null==o?void 0:o.l2Cache)&&(T.current=Z(R,[C.v]),O=B(T.current).l2Cache);const k=(M=s||T.current||"",`alova/form-${q(M,t.Method)?G(M):M}`);var M;const P=m(d),j=m(Mt(w?i.serializers:c));s&&!Ar[s]&&(Ar[s]={form:C.v,l2Cache:O,config:r});const N=nt(((...e)=>R(C.v,...e)),{...r,__referingObj:b,middleware:v?(e,t)=>v({...e,delegatingActions:{updateForm:D,reset:A}},t):c,immediate:_||o?d:h}),A=()=>{P.current=u;const e=Dr(a);e&&(C.v=e),_&&(null==O||O.remove(k))},D=e=>{C.v={...C.v,...e}},U=S({...N,...E([C]),updateForm:D,reset:A,onRestore(e){x.on(Lr,e)}}),H=m(L),{send:F,onSuccess:I}=U;return p((()=>{if(s&&(H.current=Nr.on(s,(e=>{C.v=e}))),_&&O&&!o){const e=j.current.deserialize(O.get(k));e&&(C.v=e,x.emit(Lr,c)),h&&F()}})),y([C],(()=>{s&&(Nr.emit(s,C.v),Ar[s]&&(Ar[s].form=C.v)),!P.current&&_?null==O||O.set(k,j.current.serialize(C.v)):P.current=d})),g((()=>{H.current()})),I((()=>{l&&A()})),U},e.usePagination=(e,r={})=>{const{create:s,computed:a,ref:i,watch:l,exposeProvider:h,objectify:w,__referingObj:x}=Ce(t.promiseStatesHook()),{preloadPreviousPage:T=u,preloadNextPage:O=u,total:M=e=>e.total,data:P=e=>e.data,append:N=d,initialPage:U=1,initialPageSize:H=10,watchingStates:F=[],initialData:$,immediate:B=u,middleware:Q,force:z=L,actions:W={},...K}=r,V=i(e),Y=i(d),Z=s(U,"page"),te=s(H,"pageSize"),re=s($&&P($)||[],"data"),ne=s($?M($):c,"total"),{snapshots:oe,get:se,save:ae,remove:ie}=i((e=>{let r={};return{snapshots:()=>r,save(e,t=d){const n=G(e);r[n]&&!t||(r[n]={entity:e})},get:n=>r[G(q(n,t.Method)?n:e(n))],remove(e){e?delete r[e]:r={}}}})((e=>V.current(e,te.v)))).current,ce=e=>P(e)||e,le=rt({__referingObj:x,updateState:d,force:({args:e})=>e[C(e)-1]}),{loading:ue,fetch:de,abort:he,onSuccess:ve}=le,fe=i(ue),me=(t=Z.v,r=[])=>{const n=e(t,te.v,...r);return ae(n),n};l(F,(()=>{Z.v=U,Y.current=u}));const pe=i({}),ge=s("","status"),ye=s([],"removing"),Ee=s(c,"replacing"),Se=a((()=>{const e=ne.v;return e!==c?Math.ceil(e/te.v):c}),[te,ne],"pageCount"),be=e=>(...t)=>pe.current[e](...t),we=ot(((...e)=>{const[t,,r]=it(e);return me(t,r)}),[...F,Z.e,te.e],{__referingObj:x,immediate:B,initialData:$,managedStates:w([re,Z,te,ne],"s"),middleware:(e,t)=>Q?Q({...e,delegatingActions:{refresh:be("refresh"),insert:be("insert"),remove:be("remove"),replace:be("replace"),reload:be("reload"),getState:e=>({page:Z,pageSize:te,data:re,pageCount:Se,total:ne,isLastPage:ke}[e].v)}},t):t(),force:e=>e.args[1]||(A(z)?z(e):z),...K}),{send:_e}=we,Re=we.__proxyState("data"),xe=async e=>{const{rawData:r=Re.v,preloadPage:n,fetchMethod:o,forceRequest:s=d,isNextPage:a=d}=e,i=Se.v,c=i?n>i:a?C(ce(r))<te.v:d;if(!(n>0&&!c))return d;const{e:l}=X(o),u=await t.queryCache(o);return l(j)<=I()?d:s||!u},Te=async(e,t,r=[])=>{const n=Z.v+1,o=me(n,r);O&&await xe({rawData:e,preloadPage:n,fetchMethod:o,isNextPage:u,forceRequest:t})&&v(de(o,...r,t),L)},Oe=()=>{const e=Re.v;if(!e)return u;const t=ce(e),r=Z.v,n=Se.v,o=R(t)?C(t):0;return n?r>=n:o<te.v},ke=s(Oe(),"isLastPage");l([Z,Se,Re,te],(async()=>{m((()=>{ke.v=Oe()}))}));const Me=async()=>{const e=se(Z.v);e&&await t.setCache(e.entity,(e=>{if(e){const t=ce(e)||[];return _(t,0,C(t),...re.v),e}}))};ve((({method:e,data:t})=>{const r=se(Z.v);if(r&&G(r.entity)===G(e)){const e=ce(t);if(N){const t=re.v,r=te.v,n=C(t)%r;if(n>0){const t=[...re.v];_(t,(Z.v-1)*r,n,...e),re.v=t}}else re.v=e}}));const Pe=i(c),je=i(c);we.onSuccess((({data:e,args:t,method:r})=>{const[n,o,s]=it(t),{total:a}=se(r)||{},i=e;ne.v=a!==c?a:M(i),o||((async(e,t=[])=>{const r=Z.v-1,n=me(r,t);T&&await xe({rawData:e,preloadPage:r,fetchMethod:n})&&v(de(n,...t,c),L)})(i,s),Te(i,d,s));const l=te.v,u=ce(i);if(st(R(u),"Got wrong array, did you return the correct array of list in `data` function"),N){if(Y.current&&(re.v=[]),n===c)re.v=[...re.v,...u];else if(n){const e=[...re.v];_(e,(n-1)*l,l,...u),re.v=e}}else re.v=u})).onSuccess((({data:e})=>{var t;null===(t=Pe.current)||void 0===t||t.call(Pe,e)})).onError((({error:e})=>{var t;null===(t=je.current)||void 0===t||t.call(je,e)})).onComplete((()=>{Y.current=d}));const Le=e=>{const t=re.v.indexOf(e);return st(t>=0,"item is not found in list"),t},{addQueue:Ne,onComplete:Ae}=i(ee()).current,De=async(t=Z.v)=>{let r=t,n=o();if(N){if(!D(t)){const e=Le(t);r=Math.floor(e/te.v)+1}st(r<=Z.v,"refresh page can't greater than page"),n=_e(r,u)}else st(D(r),"unable to calculate refresh page by item in pagination mode"),n=r===Z.v?_e(c,u):de(e(r,te.v),u);return n},Ue=async(e=d)=>{const r=Z.v,n=oe();let o=g(n);if(e)ie();else{const e=S(b([se(r-1),se(r),se(r+1)],Boolean),(({entity:e})=>G(e)));o=S(b(p(n),(t=>!k(e,t))),(e=>{const t=n[e];return delete n[e],t}))}await t.invalidateCache(S(o,(({entity:e})=>e)))},He=async()=>{fe.current&&he(),await Ue();const e=se(Z.v+1);if(e){const r=ce(await t.queryCache(e.entity)||{})||[];Te(c,C(r)<te.v)}},Fe=e=>{if(0===e)return;const t=ne.v;if(D(t)){const r=Math.max(t+e,0);ne.v=r;const n=Z.v;y([se(n-1),se(n),se(n+1)],(e=>{e&&(e.total=r)}))}},$e=(e,r=0)=>(Ae(He),Ne((async()=>{const n=D(r)?r:Le(r)+1;st(n>=0,"illegal insert position"),A(W.insert)&&(ge.v="inserting",await f(W.insert(e,r),(()=>{ge.v=""})));let o=c;const s=[...re.v];if(C(s)%te.v==0&&(o=s.pop()),_(s,n,0,e),re.v=s,Fe(1),await Me(),o){const e=se(Z.v+1);e&&await t.setCache(e.entity,(e=>{if(e){const t=ce(e)||[];return t.unshift(o),t.pop(),e}}))}}))),qe=(...e)=>(Ae(He),Ne((async()=>{const r=S(e,(e=>{const t=D(e)?e:Le(e);return at(t,re.v),t}));if(A(W.remove)){ge.v="removing",ye.v=[...r];const t=(o=S(e,(e=>W.remove(e))),n.all(o));await f(t,(()=>{ge.v="",ye.v=[]}))}var o;const s=Z.v,a=se(s+1),i=[];a&&await t.setCache(a.entity,(e=>{if(e){const t=ce(e);return R(t)&&E(i,..._(t,0,C(r))),e}}));const c=Oe(),l=C(i);let u=!1;if(l>0||c){const e=b(re.v,((e,t)=>!k(r,t)));u=!N&&c&&C(e)<=0,!u&&l>0&&E(e,...i),re.v=e}else l<=0&&!c&&De(s);return Fe(-C(r)),Me().then((()=>{u&&(Z.v=s-1)}))}))),Ie=(e,t)=>Ne((async()=>{st(t!==c,"expect specify the replace position");const r=D(t)?t:Le(t);at(r,re.v),A(W.replace)&&(ge.v="replacing",Ee.v=r,await f(W.replace(e,t),(()=>{ge.v="",Ee.v=c})));const n=[...re.v];_(n,r,1,e),re.v=n,await Me()})),Be=async()=>{await Ue(u),Y.current=u,Z.v===U?v(_e(),L):Z.v=U;const{resolve:e,reject:t,promise:r}=J();return Pe.current=e,je.current=t,r};return pe.current={refresh:De,insert:$e,remove:qe,replace:Ie,reload:Be},h({...we,...w([re,Z,Se,te,ne,ke,ge,ye,Ee]),send:(...e)=>_e(...e,c,c),fetching:le.loading,onFetchSuccess:le.onSuccess,onFetchError:le.onError,onFetchComplete:le.onComplete,refresh:De,insert:$e,remove:qe,replace:Ie,reload:Be})},e.useRequest=nt,e.useRetriableRequest=(e,r={})=>{const{retry:n=3,backoff:a={delay:1e3},middleware:i=L}=r,{ref:l,exposeProvider:h,__referingObj:f}=Ce(t.promiseStatesHook()),p=ie(),g=l(0),y=l(c),E=l(c),S=l(c),b=l(d),w=l(c),_=l(J()),C=nt(e,{...r,__referingObj:f,middleware(e,t){i({...e,delegatingActions:{stop:R}},(()=>o()));const{proxyStates:r,args:l,send:h,method:f,controlLoading:C}=e;C();const{loading:x}=r,T=(e=d)=>{x.v=e},O=e=>{T(),r.error.v=e,clearTimeout(w.current),((e,t,r)=>{m((()=>{p.emit(Hr,Y(Ve,Ne.spawn(e,t),r,g.current)),y.current=c,g.current=0}))})(f,l,e)};return x.v||v(_.current.promise,(e=>{O(e),_.current=J()})),T(u),b.current=u,E.current=f,S.current=l,t().then((e=>(T(),e)),(e=>{if(!y.current&&(D(n)?g.current<n:n(e,...l))){g.current+=1;const e=re(a,g.current);w.current=m((()=>{p.emit(Ur,Y(Ke,Ne.spawn(f,l),g.current,e)),v(h(...l),L)}),e)}else e=y.current||e,O(e);return s(e)})).finally((()=>{b.current=d}))}}),R=()=>{$r(C.__proxyState("loading").v,"there is no requests being retried"),y.current=Y(ne,Fr,"stop retry manually"),b.current?C.abort():_.current.reject(y.current)};return h({...C,stop:R,onRetry:e=>{p.on(Ur,(t=>e(t)))},onFail:e=>{p.on(Hr,(t=>e(t)))}})},e.useSQRequest=function(e,r={}){const{exposeProvider:n,__referingObj:o}=Ce(t.promiseStatesHook()),{middleware:s=L}=r,{c:a,m:i,b:c,d:l}=lr(e,r),u=nt(a,{...r,__referingObj:o,middleware:(e,t)=>{const r=i(e,t);return s(e,(()=>r)),r}});return l(u),n({...u,...c})},e.useSSE=(e,r={})=>{const{initialData:n,withCredentials:a,interceptByGlobalResponded:i=u,immediate:l=d,responseType:v="text",reconnectionTime:m=null,...g}=r,E=u,{create:w,ref:_,onMounted:C,onUnmounted:R,objectify:x,exposeProvider:T,memorize:O}=Ce(t.promiseStatesHook()),k=_([]),M=_(c),P=_(c),j=w(n,"data"),D=w(wr.CLOSED,"readyState"),H=w(c,"eventSource");let I,z=we(e);const G=ie(),W=_(new Map),K=_(N),V=_(Se),X=_(L),Z=e=>{const{responded:t}=(e=>B(e).options)(e);if(I=t,A(I))K.current=I;else if(I&&$(I)){const{onSuccess:e,onError:t,onComplete:r}=I;K.current=A(e)?e:K.current,V.current=A(t)?t:V.current,X.current=A(r)?r:X.current}},ee=async e=>{const{headers:r,transform:n=N}=Q(z),o=await e,s=await n(o,r||{});return j.v=s,t.hitCacheBySource(z),s},te=async(e,t)=>{Cr(M.current,"EventSource is not initialized");const r=M.current,n=Y(fr,Ne.spawn(z,k.current),r);if(e===Rr.Open)return n;const a=i?K.current:N,c=i?V.current:Se,l=i?X.current:L,u=U(t);if("json"===v&&u)try{d=t,t=JSON.parse(d)}catch(e){throw Y(ne,_r,e.message)}var d;const m=f(h(u?o(t):s(t),(e=>ee(a(e,z))),(e=>ee(c(e,z)))),(()=>{l(z)}));return h(m,(e=>Y(pr,n,e)),(e=>new mr(n,e)))},re=e=>t=>t.error===c?e(t):G.emit(br,t),oe=O((()=>{var e;D.v=wr.OPEN,h(te(Rr.Open),(e=>G.emit(Er,e))),null===(e=P.current)||void 0===e||e.resolve()})),se=O((e=>{var t;D.v=wr.CLOSED,h(te(Rr.Error,e.error||Y(Error,"SSE Error")),re((e=>G.emit(Sr,e)))),null===(t=P.current)||void 0===t||t.resolve()})),ae=O((e=>{h(te(Rr.Message,e.data),re((e=>G.emit(Sr,e))))})),ce=()=>{const e=M.current;e&&(P.current&&P.current.resolve(),e.close(),e.removeEventListener(Rr.Open,oe),e.removeEventListener(Rr.Error,se),e.removeEventListener(Rr.Message,ae),e.removeEventListener(Rr.Close,ce),D.v=wr.CLOSED,W.current.forEach((([t,r],n)=>{e.removeEventListener(n,r)})))},le=(...t)=>{let r=M.current,n=P.current;r&&E&&ce(),n||(n=P.current=J(),n&&f(n.promise,(()=>{n=c}))),k.current=t,z=we(e,t),Z(z);const{params:o,headers:s}=Q(z),{baseURL:i,url:l,data:u,type:d}=z,v=((e,t,r)=>{const n=/^https?:\/\//i.test(t);n||(e=e.endsWith("/")?e.slice(0,-1):e,""!==t&&(t=t.startsWith("/")?t:`/${t}`));const o=n?t:e+t,s=U(r)?r:S(b(p(r),(e=>r[e]!==c)),(e=>`${e}=${r[e]}`)).join("&");return s?+o.includes("?")?`${o}&${s}`:`${o}?${s}`:o})(i,l,o);var y,w,_;return r=Y(yr,v,m,{credentials:a?"include":"same-origin",method:d||"GET",headers:s,body:(e=>U(e)||(e=>{const t=F(e);return/^\[object (Blob|FormData|ReadableStream|URLSearchParams)\]$/i.test(t)||q(e,ArrayBuffer)})(e))(u)?u:(y=u,JSON.stringify(y,w,_)),...g}),M.current=r,H.v=r,D.v=wr.CONNECTING,r.addEventListener(Rr.Open,oe),r.addEventListener(Rr.Error,se),r.addEventListener(Rr.Message,ae),r.addEventListener(Rr.Close,ce),W.current.forEach((([e,t],n)=>{null==r||r.addEventListener(n,(e=>{h(te(n,e.data),re(t))}))})),n.promise};return R((()=>{ce(),G.off(Er),G.off(Sr),G.off(br),W.current.forEach((([e,t,r])=>{r()}))})),C((()=>{var e;l&&(le(),null===(e=P.current)||void 0===e||e.promise.catch((()=>{})))})),T({send:le,close:ce,on:(e,t)=>{var r;const n=W.current;if(!n.has(e)){const t=function(e=L){let t=[];return[r=>(t.includes(r)||(t.push(r),e(t)),()=>{t=b(t,(e=>e!==r)),e(t)}),(...e)=>{if(t.length>0)return y(t,(t=>t(...e)))},()=>{t=[],e(t)}]}((r=>{var n;0===r.length&&(null===(n=M.current)||void 0===n||n.removeEventListener(e,t[1]),W.current.delete(e))})),o=t[1];n.set(e,t),null===(r=M.current)||void 0===r||r.addEventListener(e,(t=>{h(te(e,t.data),re(o))}))}const[o]=n.get(e);return o(t)},onMessage:e=>{G.on(Sr,e)},onError:e=>{G.on(br,e)},onOpen:e=>{G.on(Er,e)},...x([D,j,H])})},e.useSerialRequest=(e,r={})=>{ct("useSerialRequest",e);const{ref:n,__referingObj:o}=Ce(t.promiseStatesHook()),s=n([]).current,a=nt(e[0],{...r,__referingObj:o,middleware:lt(e,r.middleware,s)});return a.onError=ce(a.onError,((e,t)=>{t.method=s[C(s)-1],e(t)})),a},e.useSerialWatcher=(e,r,n={})=>{ct("useSerialWatcher",e);const{ref:o,__referingObj:s}=Ce(t.promiseStatesHook()),a=o([]).current,i=ot(e[0],r,{...n,__referingObj:s,middleware:lt(e,n.middleware,a)});return i.onError=ce(i.onError,((e,t)=>{t.method=a[C(a)-1],e(t)})),i},e.useUploader=Tr,e.useWatcher=ot}));
